#!/usr/bin/env python3
"""
直接测试API端点
"""

import requests
import json

def test_api_direct():
    """直接测试API端点"""
    print("🔍 直接测试API端点")
    print("="*50)
    
    # 1. 登录获取token
    login_data = {
        'username': 'admin',
        'password': 'admin123'
    }
    
    try:
        login_response = requests.post('http://localhost:8000/api/auth/login', data=login_data, timeout=10)
        
        if login_response.status_code == 200:
            login_result = login_response.json()
            token = login_result.get('access_token')
            headers = {'Authorization': f'Bearer {token}', 'Content-Type': 'application/json'}
            
            print(f"✅ 登录成功")
            
            # 2. 直接调用移除学生API
            class_id = 1
            student_id = 34  # 从之前的调试数据中知道这个学生存在
            
            print(f"\n直接调用移除学生API:")
            print(f"URL: http://localhost:8000/api/classes/{class_id}/students/{student_id}/")
            print(f"Method: DELETE")
            print(f"Headers: {headers}")
            
            remove_response = requests.delete(
                f'http://localhost:8000/api/classes/{class_id}/students/{student_id}/',
                headers=headers,
                timeout=10
            )
            
            print(f"\n响应状态码: {remove_response.status_code}")
            print(f"响应内容: {remove_response.text}")
            
            # 3. 检查响应头
            print(f"\n响应头:")
            for key, value in remove_response.headers.items():
                print(f"  {key}: {value}")
                
        else:
            print(f"❌ 登录失败: {login_response.text}")
    
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_api_direct()
