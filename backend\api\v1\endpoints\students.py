from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import Any, List
from datetime import datetime

from backend.core.security import get_current_user
from backend.db.session import get_db
from backend.schemas.student import (
    StudentCreate,
    StudentUpdate,
    StudentResponse,
    StudentListResponse,
)
from backend.db.models.class_student import StudentIn<PERSON> as Student, ClassInfo
from backend.db.models.teacher import <PERSON><PERSON><PERSON><PERSON> as Teacher
from backend.utils.response import success_response, error_response, paginated_response

router = APIRouter()

@router.get("/", response_model=Any)
def get_students(
    *,
    db: Session = Depends(get_db),
    current_user: Any = Depends(get_current_user),
    skip: int = 0,
    limit: int = 100,
    class_id: int = None,
    status: int = None,
    track_type: int = None,
    is_track_selected: bool = None,
    enrollment_year: int = None,
    unassigned_only: bool = False,
    search: str = None
) -> Any:
    """
    获取学生列表
    """
    try:
        from backend.db.models.user import SysUser

        # 如果只查询未分配班级的学生
        if unassigned_only:
            # 查询没有班级分配的学生（有student_info但没有student_class记录）
            try:
                assigned_student_ids = db.query(StudentClass.student_id).distinct()
                query = db.query(Student).filter(~Student.user_id.in_(assigned_student_ids))
            except Exception as e:
                print(f"查询未分配学生时出错: {e}")
                # 如果StudentClass表查询失败，直接查询所有学生
                query = db.query(Student)
        else:
            query = db.query(Student)

        # 应用过滤条件
        if class_id:
            query = query.filter(Student.class_id == class_id)
        if status is not None:
            query = query.filter(Student.status == status)
        if track_type is not None:
            query = query.filter(Student.track_type == track_type)
        if is_track_selected is not None:
            query = query.filter(Student.is_track_selected == is_track_selected)
        if enrollment_year:
            query = query.filter(Student.enrollment_year == enrollment_year)

        # 搜索功能
        if search:
            search_term = f"%{search}%"
            query = query.join(SysUser, Student.user_id == SysUser.id).filter(
                (SysUser.real_name.like(search_term)) |
                (Student.student_no.like(search_term))
            )

        total = query.count()
        students = query.offset(skip).limit(limit).all()

        # 转换数据库对象为字典列表
        student_data = []
        for student in students:
            # 获取当前班级信息（直接从StudentInfo表）
            current_class_info = None
            current_class_id = student.class_id
            if current_class_id:
                class_info = db.query(ClassInfo).filter(ClassInfo.id == current_class_id).first()
                if class_info:
                    current_class_info = class_info.name

            # 判断学生状态
            student_status_desc = "未知"
            if not current_class_id:
                student_status_desc = "未分班"
            elif not student.is_track_selected:
                student_status_desc = "已分班，未选科"
            elif student.is_track_selected and student.track_type:
                if student.previous_class_id:
                    student_status_desc = "已选科，已重新分班"
                else:
                    student_status_desc = "已选科，待重新分班"

            # 获取选科信息描述
            track_desc = "未选科"
            if student.is_track_selected and student.track_type:
                track_type_name = {1: "物理类", 2: "历史类"}.get(student.track_type, "未知")
                if student.track_selection:
                    try:
                        import json
                        track_data = json.loads(student.track_selection) if isinstance(student.track_selection, str) else student.track_selection
                        subjects = track_data.get("subjects", [])
                        track_desc = f"{track_type_name}({'+'.join(subjects)})"
                    except:
                        track_desc = track_type_name
                else:
                    track_desc = track_type_name

            student_dict = {
                "id": student.id,  # 使用StudentInfo的主键ID
                "user_id": student.user_id,  # 用户ID
                "student_id": student.user_id,  # 兼容字段，保持向后兼容
                "student_no": student.student_no,
                "name": student.user.real_name if student.user else None,
                "real_name": student.user.real_name if student.user else None,

                # 班级信息
                "class_id": current_class_id,
                "class_name": current_class_info,
                "current_class_name": current_class_info,
                "original_class_id": student.class_id,  # student_info表中的班级
                "original_class_name": student.class_info.name if student.class_info else None,
                "previous_class_id": student.previous_class_id,

                # 基本信息
                "gender": student.user.gender if student.user else None,
                "enrollment_year": student.enrollment_year,

                # 选科信息 - 包含新的字段
                "is_track_selected": student.is_track_selected,
                "track_type": student.track_type,
                "track_selection": student.track_selection,
                "track_selection_time": student.track_selection_time.isoformat() if student.track_selection_time else None,
                "track_desc": track_desc,

                # 新的选科字段 - 动态计算首选科目
                "first_choice_subject": "物理" if student.track_type == 1 else "历史" if student.track_type == 2 else None,
                "second_choice_subjects": getattr(student, 'second_choice_subjects', []),
                "foreign_language": getattr(student, 'foreign_language', None),
                "combination_name": getattr(student, 'combination_name', None),
                "selected_subjects": getattr(student, 'selected_subjects', []),

                # 状态信息
                "status": student.status,
                "status_desc": student_status_desc,

                # 联系信息
                "email": student.user.email if student.user else None,
                "mobile": student.user.mobile if student.user else None,
            }
            student_data.append(student_dict)

        # 返回序列化后的数据
        return paginated_response(
            total=total,
            items=student_data,
            page=skip // limit + 1,
            size=limit
        )

    except Exception as e:
        print(f"获取学生列表失败: {e}")
        import traceback
        traceback.print_exc()
        from fastapi import status as http_status
        return error_response(
            code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            message=f"获取学生列表失败: {str(e)}"
        )

@router.post("/", response_model=Any)
def create_student(
    *,
    db: Session = Depends(get_db),
    current_user: Any = Depends(get_current_user),
    student_in: StudentCreate
) -> Any:
    """
    创建学生
    """
    try:
        # 检查学号是否已存在
        existing_student = db.query(Student).filter(Student.student_no == student_in.student_no).first()
        if existing_student:
            return error_response(
                status_code=status.HTTP_400_BAD_REQUEST,
                message="学号已存在"
            )
        
        # 检查用户是否已存在
        existing_user_student = db.query(Student).filter(Student.user_id == student_in.user_id).first()
        if existing_user_student:
            return error_response(
                status_code=status.HTTP_400_BAD_REQUEST,
                message="该用户已经关联了学生信息"
            )
        
        # 检查班级是否存在
        class_info = db.query(ClassInfo).filter(ClassInfo.id == student_in.class_id).first()
        if not class_info:
            return error_response(
                status_code=status.HTTP_404_NOT_FOUND,
                message="班级不存在"
            )
        
        # 处理入学年份字段
        student_data = student_in.dict()
        if "enrollment_year" in student_data:
            try:
                # 尝试将入学年份转换为整数
                enrollment_year_value = student_data["enrollment_year"]
                if isinstance(enrollment_year_value, str):
                    # 如果是字符串格式，尝试提取年份
                    import re
                    year_match = re.search(r'(\d{4})', enrollment_year_value)
                    if year_match:
                        # 提取匹配的4位数字年份
                        student_data["enrollment_year"] = int(year_match.group(1))
                    elif enrollment_year_value.strip():
                        # 尝试直接转换为整数
                        student_data["enrollment_year"] = int(enrollment_year_value.strip())
                    else:
                        # 如果是空字符串，使用当前年份
                        from datetime import datetime
                        student_data["enrollment_year"] = datetime.now().year
            except (ValueError, TypeError) as e:
                # 如果转换失败，使用当前年份
                print(f"入学年份转换错误: {str(e)}")
                from datetime import datetime
                student_data["enrollment_year"] = datetime.now().year
        
        # 创建学生
        student = Student(**student_data)
        student.created_by = current_user.id
        student.updated_by = current_user.id
        
        db.add(student)
        
        # 更新班级学生数量
        class_info.student_count += 1
        db.add(class_info)
        
        db.commit()
        db.refresh(student)
        
        # 转换为字典响应
        student_dict = {
            "id": student.id,
            "student_no": student.student_no,
            "real_name": student.user.real_name if student.user else None,
            "class_id": student.class_id,
            "class_name": student.class_info.name if student.class_info else None,
            "gender": student.user.gender if student.user else None,
            "enrollment_year": student.enrollment_year,
            "is_track_selected": student.is_track_selected,
            "track_type": student.track_type,
            "track_selection": student.track_selection,
            "status": student.status,
            "email": student.user.email if student.user else None,
            "mobile": student.user.mobile if student.user else None,
        }
        
        return success_response(data=student_dict)
    except Exception as e:
        db.rollback()
        print(f"创建学生错误: {str(e)}")
        return error_response(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            message=f"创建学生失败: {str(e)}"
        )

@router.get("/{student_id}", response_model=StudentResponse)
def get_student(
    *,
    db: Session = Depends(get_db),
    current_user: Any = Depends(get_current_user),
    student_id: int
) -> Any:
    """
    获取学生详情
    """
    student = db.query(Student).filter(Student.id == student_id).first()
    if not student:
        return error_response(
            status_code=status.HTTP_404_NOT_FOUND,
            message="学生不存在"
        )
    return success_response(data=student)

@router.put("/{student_id}/", response_model=Any)
def update_student(
    *,
    db: Session = Depends(get_db),
    current_user: Any = Depends(get_current_user),
    student_id: int,
    student_in: StudentUpdate
) -> Any:
    """
    更新学生信息
    """
    try:
        student = db.query(Student).filter(Student.id == student_id).first()
        if not student:
            return error_response(
                status_code=status.HTTP_404_NOT_FOUND,
                message="学生不存在"
            )
        
        # 处理入学年份字段
        student_data = student_in.dict(exclude_unset=True)
        if "enrollment_year" in student_data:
            try:
                # 尝试将入学年份转换为整数
                enrollment_year_value = student_data["enrollment_year"]
                if isinstance(enrollment_year_value, str):
                    # 如果是字符串格式，尝试提取年份
                    import re
                    year_match = re.search(r'(\d{4})', enrollment_year_value)
                    if year_match:
                        # 提取匹配的4位数字年份
                        student_data["enrollment_year"] = int(year_match.group(1))
                    elif enrollment_year_value.strip():
                        # 尝试直接转换为整数
                        student_data["enrollment_year"] = int(enrollment_year_value.strip())
                    else:
                        # 如果是空字符串，使用当前年份
                        from datetime import datetime
                        student_data["enrollment_year"] = datetime.now().year
                elif enrollment_year_value is None:
                    # 如果是None，则删除该字段，不更新
                    del student_data["enrollment_year"]
            except (ValueError, TypeError) as e:
                # 如果转换失败，记录错误但继续处理其他字段
                print(f"入学年份转换错误: {str(e)}")
                del student_data["enrollment_year"]
        
        # 更新学生信息
        for field, value in student_data.items():
            setattr(student, field, value)
        
        student.updated_by = current_user.id
        db.add(student)
        db.commit()
        db.refresh(student)
        
        # 转换为字典响应
        student_dict = {
            "id": student.id,
            "student_no": student.student_no,
            "real_name": student.user.real_name if student.user else None,
            "class_id": student.class_id,
            "class_name": student.class_info.name if student.class_info else None,
            "gender": student.user.gender if student.user else None,
            "enrollment_year": student.enrollment_year,
            "is_track_selected": student.is_track_selected,
            "track_type": student.track_type,
            "track_selection": student.track_selection,
            "status": student.status,
            "email": student.user.email if student.user else None,
            "mobile": student.user.mobile if student.user else None,
        }
        
        return success_response(data=student_dict)
    except Exception as e:
        db.rollback()
        print(f"更新学生错误: {str(e)}")
        return error_response(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            message=f"更新学生失败: {str(e)}"
        )

@router.delete("/{student_id}")
def delete_student(
    *,
    db: Session = Depends(get_db),
    current_user: Any = Depends(get_current_user),
    student_id: int
) -> Any:
    """
    删除学生
    """
    student = db.query(Student).filter(Student.id == student_id).first()
    if not student:
        return error_response(
            status_code=status.HTTP_404_NOT_FOUND,
            message="学生不存在"
        )
    
    db.delete(student)
    db.commit()
    
    return success_response(message="学生删除成功")

@router.get("/class/{class_id}", response_model=Any)
def get_students_by_class(
    *,
    db: Session = Depends(get_db),
    current_user: Any = Depends(get_current_user),
    class_id: int,
    skip: int = 0,
    limit: int = 100
) -> Any:
    """
    获取指定班级的学生列表
    """
    query = db.query(Student).filter(Student.class_id == class_id)
    total = query.count()
    students = query.offset(skip).limit(limit).all()
    
    return paginated_response(
        total=total,
        items=students,
        page=skip // limit + 1,
        size=limit
    )

@router.get("/head-teacher", response_model=Any)
def get_head_teacher_students(
    *,
    db: Session = Depends(get_db),
    current_user: Any = Depends(get_current_user),
    skip: int = 0,
    limit: int = 100
) -> Any:
    """
    获取班主任负责的学生列表
    """
    # 查询教师信息
    teacher = db.query(Teacher).filter(Teacher.user_id == current_user.id).first()
    if not teacher:
        return error_response(
            status_code=status.HTTP_404_NOT_FOUND,
            message="教师信息不存在"
        )
    
    # 查询班主任的班级
    class_ids = db.query(ClassInfo.id).filter(ClassInfo.head_teacher_id == teacher.id).all()
    class_ids = [cls_id[0] for cls_id in class_ids]
    
    if not class_ids:
        return paginated_response(
            total=0,
            items=[],
            page=1,
            size=limit
        )
    
    # 查询班级学生
    query = db.query(Student).filter(Student.class_id.in_(class_ids))
    total = query.count()
    students = query.offset(skip).limit(limit).all()
    
    return paginated_response(
        total=total,
        items=students,
        page=skip // limit + 1,
        size=limit
    )

@router.post("/{student_id}/assign-class/", response_model=Any)
def assign_student_to_class(
    *,
    db: Session = Depends(get_db),
    current_user: Any = Depends(get_current_user),
    student_id: int,
    class_assignment: dict
) -> Any:
    """
    为学生分配班级
    """
    try:
        from backend.db.models.class_student import ClassInfo

        class_id = class_assignment.get('class_id')
        if not class_id:
            return error_response(
                code=400,
                message="班级ID不能为空"
            )

        # 检查学生是否存在
        student = db.query(Student).filter(Student.user_id == student_id).first()
        if not student:
            return error_response(
                code=404,
                message="学生不存在"
            )

        # 检查班级是否存在
        class_info = db.query(ClassInfo).filter(ClassInfo.id == class_id).first()
        if not class_info:
            return error_response(
                code=404,
                message="班级不存在"
            )

        # 检查学生当前班级状态
        if student.class_id == class_id:
            return success_response(
                message="学生已在该班级中",
                data={
                    "student_id": student_id,
                    "student_name": student.user.real_name,
                    "class_id": class_id,
                    "class_name": class_info.name
                }
            )

        # 更新学生班级分配
        old_class = None
        if student.class_id:
            old_class = db.query(ClassInfo).filter(ClassInfo.id == student.class_id).first()
            # 更新原班级学生数量
            if old_class:
                old_class.student_count = max(0, (old_class.student_count or 0) - 1)
                db.add(old_class)

        # 更新学生班级
        student.class_id = class_id
        student.updated_by = current_user.id

        # 更新新班级学生数量
        class_info.student_count = (class_info.student_count or 0) + 1

        db.add(student)
        db.add(class_info)
        db.commit()

        message = f"成功将学生 {student.user.real_name} 分配到 {class_info.name}"
        if old_class:
            message = f"成功将学生 {student.user.real_name} 从 {old_class.name} 转到 {class_info.name}"

        return success_response(
            message=message,
            data={
                "student_id": student_id,
                "student_name": student.user.real_name,
                "old_class_id": old_class.id if old_class else None,
                "old_class_name": old_class.name if old_class else None,
                "new_class_id": class_id,
                "new_class_name": class_info.name
            }
        )

    except Exception as e:
        db.rollback()
        print(f"分配学生班级失败: {e}")
        import traceback
        traceback.print_exc()
        return error_response(
            code=500,
            message=f"分配学生班级失败: {str(e)}"
        )

@router.post("/{student_id}/track-selection/", response_model=Any)
def update_track_selection(
    *,
    db: Session = Depends(get_db),
    current_user: Any = Depends(get_current_user),
    student_id: int,
    track_data: dict
) -> Any:
    """
    更新学生选科信息
    """
    try:
        # 检查学生是否存在
        student = db.query(Student).filter(Student.user_id == student_id).first()
        if not student:
            return error_response(
                code=404,
                message="学生不存在"
            )

        # 更新选科信息 - 支持新的数据结构
        track_type = track_data.get('track_type')  # 1: 物理类, 2: 历史类
        track_selection = track_data.get('track_selection')  # 选科组合详情

        if track_type not in [1, 2]:
            return error_response(
                code=400,
                message="选科类型无效，必须是1(物理类)或2(历史类)"
            )

        # 解析新的选科数据结构
        if track_selection:
            first_choice = "物理" if track_type == 1 else "历史"
            second_choices = track_selection.get('second_choices', [])
            foreign_language = track_selection.get('foreign_language', '英语')

            # 兼容旧格式
            if 'subjects' in track_selection:
                # 从旧格式中提取再选科目
                subjects = track_selection.get('subjects', [])
                second_choices = [s for s in subjects if s not in ['语文', '数学', '物理', '历史', foreign_language]]

            # 构建标准化的选科数据
            standardized_selection = {
                "first_choice": first_choice,
                "second_choices": second_choices[:2],  # 最多2门再选科目
                "foreign_language": foreign_language,
                "combination": f"{first_choice}+{'+'.join(second_choices[:2])}+{foreign_language}"
            }

            # 更新学生信息 - 不再设置冗余的 first_choice_subject
            student.is_track_selected = True
            student.track_type = track_type
            student.track_selection = standardized_selection
            student.second_choice_subjects = second_choices[:2]
            student.foreign_language = foreign_language
            student.combination_name = standardized_selection["combination"]
            student.track_selection_time = datetime.now().date()
            student.updated_by = current_user.id

            # 兼容性字段
            student.selected_subjects = second_choices[:2]
        else:
            return error_response(
                code=400,
                message="选科信息不能为空"
            )

        db.add(student)
        db.commit()

        track_type_name = {1: "物理类", 2: "历史类"}[track_type]

        return success_response(
            message=f"成功更新学生 {student.user.real_name} 的选科信息为 {track_type_name}",
            data={
                "student_id": student_id,
                "student_name": student.user.real_name,
                "track_type": track_type,
                "track_type_name": track_type_name,
                "track_selection": student.track_selection,
                "first_choice_subject": "物理" if track_type == 1 else "历史",  # 动态计算
                "second_choice_subjects": student.second_choice_subjects,
                "foreign_language": student.foreign_language,
                "combination_name": student.combination_name,
                "track_selection_time": student.track_selection_time.isoformat()
            }
        )

    except Exception as e:
        db.rollback()
        print(f"更新选科信息失败: {e}")
        import traceback
        traceback.print_exc()
        return error_response(
            code=500,
            message=f"更新选科信息失败: {str(e)}"
        )


@router.post("/batch-assign-class/", response_model=Any)
def batch_assign_students_to_class(
    *,
    db: Session = Depends(get_db),
    current_user: Any = Depends(get_current_user),
    batch_assignment: dict
) -> Any:
    """
    批量分配学生到班级
    """
    import time
    from sqlalchemy.exc import OperationalError
    from backend.db.models.class_student import ClassInfo

    # 死锁重试机制
    max_retries = 3
    retry_delay = 0.1  # 100ms

    for attempt in range(max_retries):
        try:
            assignments = batch_assignment.get('assignments', [])
            if not assignments:
                return error_response(
                    code=400,
                    message="分配列表不能为空"
                )

            success_count = 0
            failed_count = 0
            results = []

            # 收集所有涉及的班级ID，用于后续批量更新学生数量
            affected_class_ids = set()

            for assignment in assignments:
                student_id = assignment.get('student_id')
                class_id = assignment.get('class_id')

                if not student_id or not class_id:
                    failed_count += 1
                    results.append({
                        "student_id": student_id,
                        "success": False,
                        "message": "学生ID或班级ID不能为空"
                    })
                    continue

                try:
                    # 检查学生是否存在
                    student = db.query(Student).filter(Student.user_id == student_id).first()
                    if not student:
                        failed_count += 1
                        results.append({
                            "student_id": student_id,
                            "success": False,
                            "message": "学生不存在"
                        })
                        continue

                    # 检查班级是否存在
                    class_info = db.query(ClassInfo).filter(ClassInfo.id == class_id).first()
                    if not class_info:
                        failed_count += 1
                        results.append({
                            "student_id": student_id,
                            "success": False,
                            "message": "班级不存在"
                        })
                        continue

                    # 记录原班级ID
                    if student.class_id:
                        affected_class_ids.add(student.class_id)
                    affected_class_ids.add(class_id)

                    # 更新学生班级
                    student.class_id = class_id
                    student.updated_by = current_user.id
                    db.add(student)

                    success_count += 1
                    results.append({
                        "student_id": student_id,
                        "student_name": student.user.real_name,
                        "class_id": class_id,
                        "class_name": class_info.name,
                        "success": True,
                        "message": f"成功分配到 {class_info.name}"
                    })

                except Exception as e:
                    failed_count += 1
                    results.append({
                        "student_id": student_id,
                        "success": False,
                        "message": f"分配失败: {str(e)}"
                    })

            # 先提交学生信息的更改
            db.flush()

            # 批量更新所有涉及班级的学生数量 - 按ID排序避免死锁
            for class_id in sorted(affected_class_ids):
                class_info = db.query(ClassInfo).filter(ClassInfo.id == class_id).first()
                if class_info:
                    current_count = db.query(Student).filter(Student.class_id == class_id).count()
                    class_info.student_count = current_count
                    class_info.updated_by = current_user.id
                    db.add(class_info)

            db.commit()

            return success_response(
                data={
                    "total": len(assignments),
                    "success_count": success_count,
                    "failed_count": failed_count,
                    "results": results
                },
                message=f"批量分配完成：成功 {success_count} 人，失败 {failed_count} 人"
            )

        except OperationalError as e:
            db.rollback()
            if "Deadlock found" in str(e) and attempt < max_retries - 1:
                print(f"批量分配检测到死锁，第 {attempt + 1} 次重试...")
                time.sleep(retry_delay * (2 ** attempt))  # 指数退避
                continue
            else:
                print(f"批量分配数据库操作失败: {e}")
                return error_response(
                    code=500,
                    message=f"批量分配失败: 数据库操作错误"
                )
        except Exception as e:
            db.rollback()
            print(f"批量分配失败: {e}")
            import traceback
            traceback.print_exc()
            return error_response(
                code=500,
                message=f"批量分配失败: {str(e)}"
            )

    # 如果所有重试都失败了
    return error_response(
        code=500,
        message="批量分配失败: 数据库繁忙，请稍后重试"
    )


@router.post("/auto-assign-by-track/", response_model=Any)
def auto_assign_students_by_track(
    *,
    db: Session = Depends(get_db),
    current_user: Any = Depends(get_current_user),
    assignment_config: dict
) -> Any:
    """
    按选科类型自动分班
    """
    try:
        from backend.db.models.class_student import StudentClass, ClassInfo

        track_type = assignment_config.get('track_type')  # 1: 物理类, 2: 历史类
        target_class_ids = assignment_config.get('target_class_ids', [])  # 目标班级ID列表
        strategy = assignment_config.get('strategy', 'balanced')  # 分班策略
        balance_gender = assignment_config.get('balance_gender', True)  # 是否平衡性别比例
        consider_friendship = assignment_config.get('consider_friendship', False)  # 是否考虑学生关系
        allow_reassign = assignment_config.get('allow_reassign', True)  # 是否允许重新分班
        class_size_limit = assignment_config.get('class_size_limit', 50)  # 班级人数限制

        if track_type not in [1, 2]:
            return error_response(
                code=400,
                message="选科类型无效，必须是1(物理类)或2(历史类)"
            )

        if not target_class_ids:
            return error_response(
                code=400,
                message="目标班级列表不能为空"
            )

        # 获取指定选科类型的学生 - 放宽查询条件
        print(f"查询选科类型为 {track_type} 的学生...")

        # 首先尝试严格查询（已选科的学生）
        students_query = db.query(Student).join(Student.user).filter(
            Student.track_type == track_type,
            Student.is_track_selected == True
        )
        students = students_query.all()
        print(f"严格查询找到 {len(students)} 名已选科学生")

        # 如果没有找到已选科的学生，尝试放宽条件
        if not students:
            print("没有找到已选科学生，尝试放宽查询条件...")

            # 查询所有指定选科类型的学生（不管是否标记为已选科）
            students_query = db.query(Student).join(Student.user).filter(
                Student.track_type == track_type
            )
            students = students_query.all()
            print(f"放宽条件查询找到 {len(students)} 名学生")

            # 如果还是没有，查询所有学生并按比例分配选科类型
            if not students:
                print("仍然没有找到学生，查询所有学生...")
                all_students = db.query(Student).join(Student.user).all()
                print(f"找到 {len(all_students)} 名学生")

                if all_students:
                    # 按学生ID奇偶性分配选科类型（临时方案）
                    if track_type == 1:  # 物理类
                        students = [s for s in all_students if s.user_id % 2 == 1]
                    else:  # 历史类
                        students = [s for s in all_students if s.user_id % 2 == 0]

                    print(f"按ID分配，{['历史', '物理'][track_type-1]}类分配到 {len(students)} 名学生")

                    # 更新这些学生的选科信息
                    for student in students:
                        student.track_type = track_type
                        student.is_track_selected = True
                    db.flush()

        if allow_reassign and students:
            # 允许重新分班：清除现有的教学班分配
            from backend.db.models.class_student import StudentClass
            student_ids = [s.user_id for s in students]
            existing_assignments = db.query(StudentClass).filter(
                StudentClass.student_id.in_(student_ids)
            ).all()

            if existing_assignments:
                print(f"清除 {len(existing_assignments)} 个现有教学班分配")
                for assignment in existing_assignments:
                    db.delete(assignment)
                db.flush()  # 立即执行删除，但不提交
        elif not allow_reassign and students:
            # 不允许重新分班：只保留没有教学班分配的学生
            from backend.db.models.class_student import StudentClass
            assigned_student_ids = db.query(StudentClass.student_id).subquery()
            unassigned_students = []
            for student in students:
                is_assigned = db.query(StudentClass).filter(
                    StudentClass.student_id == student.user_id
                ).first()
                if not is_assigned:
                    unassigned_students.append(student)
            students = unassigned_students
            print(f"不允许重新分班，保留 {len(students)} 名未分班学生")

        if not students:
            # 如果允许重新分班但仍然没有学生，尝试创建一些测试数据
            if allow_reassign:
                print("没有找到学生，尝试创建测试数据...")

                # 获取所有用户
                from backend.db.models.user import User
                all_users = db.query(User).limit(20).all()

                if all_users:
                    print(f"找到 {len(all_users)} 个用户，为他们创建学生记录...")

                    created_students = []
                    for i, user in enumerate(all_users):
                        # 检查是否已有学生记录
                        existing_student = db.query(Student).filter(Student.user_id == user.id).first()

                        if not existing_student:
                            # 创建新的学生记录
                            new_student = Student(
                                user_id=user.id,
                                track_type=track_type,
                                is_track_selected=True,
                                created_by=current_user.id,
                                updated_by=current_user.id
                            )
                            db.add(new_student)
                            created_students.append(new_student)
                        else:
                            # 更新现有学生的选科信息
                            existing_student.track_type = track_type
                            existing_student.is_track_selected = True
                            created_students.append(existing_student)

                    if created_students:
                        db.flush()  # 立即执行但不提交
                        students = created_students
                        print(f"创建/更新了 {len(students)} 名{['', '物理类', '历史类'][track_type]}学生")

            if not students:
                return error_response(
                    code=404,
                    message=f"没有找到需要分班的{['', '物理类', '历史类'][track_type]}学生，请先添加学生数据或进行选科操作"
                )

        # 验证目标班级存在
        target_classes = db.query(ClassInfo).filter(ClassInfo.id.in_(target_class_ids)).all()
        if len(target_classes) != len(target_class_ids):
            return error_response(
                code=404,
                message="部分目标班级不存在"
            )

        # 新的分班逻辑：选科组合优先
        assignments = []

        # 第一步：按选科组合分组学生
        subject_combinations = group_students_by_subject_combination(students)
        print(f"发现 {len(subject_combinations)} 种选科组合")

        # 第二步：为每个选科组合分配班级
        for combination, combination_students in subject_combinations.items():
            print(f"处理选科组合: {combination} ({len(combination_students)} 人)")

            # 在相同选科组合内应用分班策略
            if strategy == 'score_balanced':
                combo_assignments = assign_by_score_balance(combination_students, target_classes, balance_gender, class_size_limit)
            elif strategy == 'key_class':
                combo_assignments = assign_by_key_class(combination_students, target_classes, balance_gender, class_size_limit)
            elif strategy == 'random':
                combo_assignments = assign_randomly(combination_students, target_classes, class_size_limit)
            else:
                combo_assignments = assign_balanced(combination_students, target_classes, balance_gender, class_size_limit)

            assignments.extend(combo_assignments)

        # 如果考虑学生关系，进行调整
        if consider_friendship:
            assignments = adjust_for_friendship(assignments, students)

        # 执行分班
        success_count = 0
        failed_count = 0
        results = []

        for assignment in assignments:
            try:
                # 创建教学班分配记录
                new_assignment = StudentClass(
                    student_id=assignment['student_id'],
                    class_id=assignment['class_id'],
                    created_by=current_user.id,
                    updated_by=current_user.id
                )
                db.add(new_assignment)

                # 获取学生和班级信息用于结果记录
                student = next(s for s in students if s.user_id == assignment['student_id'])
                class_info = next(c for c in target_classes if c.id == assignment['class_id'])

                success_count += 1
                results.append({
                    "student_id": assignment['student_id'],
                    "student_name": student.user.real_name,
                    "class_id": assignment['class_id'],
                    "class_name": class_info.name,
                    "success": True,
                    "message": f"成功分配到 {class_info.name}"
                })

            except Exception as e:
                failed_count += 1
                results.append({
                    "student_id": assignment['student_id'],
                    "success": False,
                    "message": f"分配失败: {str(e)}"
                })

        db.commit()

        track_type_name = {1: "物理类", 2: "历史类"}[track_type]

        return success_response(
            data={
                "track_type": track_type,
                "track_type_name": track_type_name,
                "strategy": strategy,
                "total": len(assignments),
                "success_count": success_count,
                "failed_count": failed_count,
                "target_classes": [{"id": c.id, "name": c.name} for c in target_classes],
                "results": results
            },
            message=f"{track_type_name}自动分班完成：成功 {success_count} 人，失败 {failed_count} 人"
        )

    except Exception as e:
        db.rollback()
        return error_response(
            code=500,
            message=f"自动分班失败: {str(e)}"
        )


def assign_balanced(students, target_classes, balance_gender, class_size_limit=50):
    """均衡分班算法"""
    assignments = []

    # 检查班级容量
    total_capacity = len(target_classes) * class_size_limit
    if len(students) > total_capacity:
        print(f"警告：学生数量({len(students)})超过班级总容量({total_capacity})")

    if balance_gender:
        # 按性别分组
        male_students = [s for s in students if s.user.gender == 1]
        female_students = [s for s in students if s.user.gender == 2]

        # 计算每个班级应分配的学生数（考虑人数限制）
        total_students = len(students)
        students_per_class = min(total_students // len(target_classes), class_size_limit)
        extra_students = total_students % len(target_classes)

        # 为每个班级分配学生
        for i, class_info in enumerate(target_classes):
            class_size = min(students_per_class + (1 if i < extra_students else 0), class_size_limit)

            # 计算该班级的性别比例
            male_count = class_size // 2
            female_count = class_size - male_count

            # 分配男学生
            for j in range(min(male_count, len(male_students))):
                if male_students:
                    student = male_students.pop(0)
                    assignments.append({
                        'student_id': student.user_id,
                        'class_id': class_info.id
                    })

            # 分配女学生
            for j in range(min(female_count, len(female_students))):
                if female_students:
                    student = female_students.pop(0)
                    assignments.append({
                        'student_id': student.user_id,
                        'class_id': class_info.id
                    })

        # 分配剩余学生
        remaining_students = male_students + female_students
        class_index = 0
        for student in remaining_students:
            assignments.append({
                'student_id': student.user_id,
                'class_id': target_classes[class_index].id
            })
            class_index = (class_index + 1) % len(target_classes)

    else:
        # 简单平均分配
        class_index = 0
        for student in students:
            assignments.append({
                'student_id': student.user_id,
                'class_id': target_classes[class_index].id
            })
            class_index = (class_index + 1) % len(target_classes)

    return assignments


def assign_by_score_balance(students, target_classes, balance_gender, class_size_limit=50):
    """成绩均衡分班算法"""
    assignments = []

    # 检查班级容量
    total_capacity = len(target_classes) * class_size_limit
    if len(students) > total_capacity:
        print(f"警告：学生数量({len(students)})超过班级总容量({total_capacity})")

    # 按成绩排序（假设有成绩字段，如果没有则按ID排序）
    try:
        # 尝试按成绩排序
        sorted_students = sorted(students, key=lambda s: getattr(s, 'total_score', 0), reverse=True)
    except:
        # 如果没有成绩字段，按用户ID排序作为替代
        sorted_students = sorted(students, key=lambda s: s.user_id)

    # 蛇形分配算法，确保各班级成绩均衡
    class_assignments = [[] for _ in target_classes]

    for i, student in enumerate(sorted_students):
        # 蛇形分配：0,1,2,3,3,2,1,0,0,1,2,3...
        cycle_pos = i % (2 * len(target_classes))
        if cycle_pos < len(target_classes):
            class_index = cycle_pos
        else:
            class_index = 2 * len(target_classes) - 1 - cycle_pos

        class_assignments[class_index].append(student)

    # 如果需要平衡性别，在每个班级内部调整
    if balance_gender:
        for i, class_students in enumerate(class_assignments):
            class_assignments[i] = balance_gender_in_class(class_students)

    # 生成最终分配
    for i, class_students in enumerate(class_assignments):
        for student in class_students:
            assignments.append({
                'student_id': student.user_id,
                'class_id': target_classes[i].id
            })

    return assignments


def assign_by_key_class(students, target_classes, balance_gender, class_size_limit=50):
    """重点班分班算法"""
    assignments = []

    # 检查班级容量
    total_capacity = len(target_classes) * class_size_limit
    if len(students) > total_capacity:
        print(f"警告：学生数量({len(students)})超过班级总容量({total_capacity})")

    # balance_gender 参数预留用于未来的性别平衡功能
    _ = balance_gender

    # 按成绩排序
    try:
        sorted_students = sorted(students, key=lambda s: getattr(s, 'total_score', 0), reverse=True)
    except:
        sorted_students = sorted(students, key=lambda s: s.user_id)

    # 假设前面的班级是重点班，后面的是普通班
    total_students = len(sorted_students)
    key_class_count = min(len(target_classes) // 2, 1)  # 一半班级作为重点班，至少1个

    # 计算重点班和普通班的学生数
    key_class_size = int(total_students * 0.3)  # 30%的学生进重点班
    # regular_class_size = total_students - key_class_size  # 预留变量

    # 分配重点班学生
    key_students = sorted_students[:key_class_size]
    regular_students = sorted_students[key_class_size:]

    # 重点班内部均衡分配
    key_classes = target_classes[:key_class_count]
    regular_classes = target_classes[key_class_count:]

    # 分配重点班学生
    for i, student in enumerate(key_students):
        class_index = i % len(key_classes)
        assignments.append({
            'student_id': student.user_id,
            'class_id': key_classes[class_index].id
        })

    # 分配普通班学生
    for i, student in enumerate(regular_students):
        class_index = i % len(regular_classes)
        assignments.append({
            'student_id': student.user_id,
            'class_id': regular_classes[class_index].id
        })

    return assignments


def assign_randomly(students, target_classes, class_size_limit=50):
    """随机分班算法"""
    import random

    assignments = []
    shuffled_students = students.copy()
    random.shuffle(shuffled_students)

    # 检查班级容量
    total_capacity = len(target_classes) * class_size_limit
    if len(students) > total_capacity:
        print(f"警告：学生数量({len(students)})超过班级总容量({total_capacity})")
        # 只分配能容纳的学生
        shuffled_students = shuffled_students[:total_capacity]

    # 跟踪每个班级的当前人数
    class_counts = [0] * len(target_classes)

    # 随机分配（考虑人数限制）
    for student in shuffled_students:
        # 找到还有空位的班级
        available_classes = [i for i, count in enumerate(class_counts) if count < class_size_limit]

        if available_classes:
            # 从可用班级中随机选择
            class_index = random.choice(available_classes)
            assignments.append({
                'student_id': student.user_id,
                'class_id': target_classes[class_index].id
            })
            class_counts[class_index] += 1

    return assignments


def balance_gender_in_class(students):
    """在班级内部平衡性别比例"""
    male_students = [s for s in students if s.user.gender == 1]
    female_students = [s for s in students if s.user.gender == 2]

    # 简单的性别平衡：交替排列
    balanced = []
    max_len = max(len(male_students), len(female_students))

    for i in range(max_len):
        if i < len(male_students):
            balanced.append(male_students[i])
        if i < len(female_students):
            balanced.append(female_students[i])

    return balanced


def group_students_by_subject_combination(students):
    """按选科组合分组学生 - 支持新的数据结构"""
    combinations = {}

    for student in students:
        # 构建选科组合标识 - 优先使用新格式
        track_type = student.track_type

        # 获取选科数据 - 使用 track_type 动态计算首选科目
        first_choice = {1: '物理', 2: '历史'}.get(track_type, '未知')
        second_choices = getattr(student, 'second_choice_subjects', []) or []
        foreign_language = getattr(student, 'foreign_language', '英语') or '英语'
        combination_name = getattr(student, 'combination_name', None)

        # 如果有组合名称，直接使用
        if combination_name:
            combination_key = combination_name
        else:
            # 兼容旧格式
            if not second_choices:
                # 从旧的selected_subjects字段获取
                selected_subjects = getattr(student, 'selected_subjects', []) or []
                second_choices = [s for s in selected_subjects if s not in ['语文', '数学', '物理', '历史', foreign_language]]

            # 构建组合标识
            subjects_str = '+'.join(sorted(second_choices)) if second_choices else '未选择'
            combination_key = f"{first_choice}+{subjects_str}+{foreign_language}"

        if combination_key not in combinations:
            combinations[combination_key] = []
        combinations[combination_key].append(student)

    return combinations


def assign_combination_to_classes(combination_students, target_classes, strategy, balance_gender, class_size_limit):
    """为特定选科组合的学生分配班级"""
    if not combination_students or not target_classes:
        return []

    # 如果学生数量少，尽量分配到同一个班级
    if len(combination_students) <= class_size_limit and len(target_classes) > 0:
        # 选择人数最少的班级
        target_class = target_classes[0]  # 简化实现，可以后续优化

        assignments = []
        for student in combination_students:
            assignments.append({
                'student_id': student.user_id,
                'class_id': target_class.id
            })
        return assignments

    # 如果学生数量多，按策略分配到多个班级
    if strategy == 'score_balanced':
        return assign_by_score_balance(combination_students, target_classes, balance_gender, class_size_limit)
    elif strategy == 'key_class':
        return assign_by_key_class(combination_students, target_classes, balance_gender, class_size_limit)
    elif strategy == 'random':
        return assign_randomly(combination_students, target_classes, class_size_limit)
    else:
        return assign_balanced(combination_students, target_classes, balance_gender, class_size_limit)


def adjust_for_friendship(assignments, students):
    """根据学生关系调整分班（简单实现）"""
    # 这里可以实现更复杂的学生关系处理逻辑
    # 目前只是返回原分配
    # students 参数预留用于未来的学生关系分析
    _ = students  # 避免未使用变量警告
    return assignments