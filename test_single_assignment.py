#!/usr/bin/env python3
"""
测试单个学生分配功能
"""

import requests
import json

def test_single_assignment():
    """测试单个学生分配功能"""
    print("🔍 测试单个学生分配功能")
    print("="*50)
    
    # 1. 登录获取token
    login_data = {
        'username': 'admin',
        'password': 'admin123'
    }
    
    try:
        login_response = requests.post('http://localhost:8000/api/auth/login', data=login_data, timeout=10)
        
        if login_response.status_code == 200:
            login_result = login_response.json()
            token = login_result.get('access_token')
            headers = {'Authorization': f'Bearer {token}', 'Content-Type': 'application/json'}
            
            print(f"✅ 登录成功")
            
            # 2. 测试单个学生分配到班级
            print(f"\n📋 测试单个学生分配")
            
            # 分配学生34到班级3（我们知道这个学生存在）
            assignment_data = {"student_id": 34}
            
            response = requests.post(
                'http://localhost:8000/api/classes/3/students/',
                json=assignment_data,
                headers=headers,
                timeout=15
            )
            
            print(f"单个分配状态码: {response.status_code}")
            print(f"单个分配响应: {response.text}")
            
            if response.status_code == 200:
                print("✅ 单个学生分配成功")
                
                # 3. 再次分配同一个学生到不同班级（测试转班）
                print(f"\n🔄 测试学生转班")
                
                transfer_response = requests.post(
                    'http://localhost:8000/api/classes/4/students/',
                    json=assignment_data,
                    headers=headers,
                    timeout=15
                )
                
                print(f"转班状态码: {transfer_response.status_code}")
                print(f"转班响应: {transfer_response.text}")
                
                if transfer_response.status_code == 200:
                    print("✅ 学生转班成功")
                else:
                    print("❌ 学生转班失败")
                    
            else:
                print("❌ 单个学生分配失败")
                
        else:
            print(f"❌ 登录失败: {login_response.text}")
    
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_single_assignment()
