#!/usr/bin/env python3
"""
测试数据库更新操作
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.db.session import SessionLocal
from backend.db.models.class_student import StudentInfo, ClassInfo

def test_database_update():
    """测试数据库更新操作"""
    print("🔍 测试数据库更新操作")
    print("="*50)
    
    db = SessionLocal()
    try:
        # 1. 查找一个有班级的学生
        student_with_class = db.query(StudentInfo).filter(
            StudentInfo.class_id.isnot(None)
        ).first()
        
        if student_with_class:
            print(f"找到学生:")
            print(f"  - ID: {student_with_class.id}")
            print(f"  - 用户ID: {student_with_class.user_id}")
            print(f"  - 当前班级ID: {student_with_class.class_id}")
            print(f"  - 原班级ID: {student_with_class.previous_class_id}")
            print(f"  - 是否已选科: {student_with_class.is_track_selected}")
            
            # 2. 尝试将class_id设置为None
            print(f"\n尝试将学生的class_id设置为None...")
            original_class_id = student_with_class.class_id
            
            try:
                student_with_class.class_id = None
                db.flush()  # 先flush看是否有错误
                print("✅ flush成功")
                
                db.commit()
                print("✅ commit成功")
                
                # 3. 验证更新
                db.refresh(student_with_class)
                print(f"更新后的class_id: {student_with_class.class_id}")
                
                # 4. 恢复原状态
                student_with_class.class_id = original_class_id
                db.commit()
                print(f"✅ 已恢复原状态: class_id = {original_class_id}")
                
            except Exception as e:
                print(f"❌ 数据库操作失败: {e}")
                db.rollback()
                import traceback
                traceback.print_exc()
        else:
            print("❌ 没有找到有班级的学生")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    test_database_update()
