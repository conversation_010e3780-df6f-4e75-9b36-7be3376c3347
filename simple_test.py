#!/usr/bin/env python3
"""
简单测试定位问题
"""

import requests
import json

def simple_test():
    """简单测试定位问题"""
    print("🔍 简单测试定位问题")
    print("="*50)
    
    # 1. 登录获取token
    login_data = {
        'username': 'admin',
        'password': 'admin123'
    }
    
    try:
        login_response = requests.post('http://localhost:8000/api/auth/login', data=login_data, timeout=10)
        
        if login_response.status_code == 200:
            login_result = login_response.json()
            token = login_result.get('access_token')
            headers = {'Authorization': f'Bearer {token}', 'Content-Type': 'application/json'}
            
            print(f"✅ 登录成功")
            
            # 2. 测试一个简单的移除操作
            print(f"\n测试移除学生 ID=2 从班级 ID=1")
            
            # 先检查学生是否在班级中
            students_response = requests.get(
                'http://localhost:8000/api/students/?limit=10',
                headers=headers,
                timeout=10
            )
            
            if students_response.status_code == 200:
                students_data = students_response.json()
                if 'data' in students_data and 'items' in students_data['data']:
                    students = students_data['data']['items']
                    target_student = None
                    for student in students:
                        if student.get('student_id') == 2:
                            target_student = student
                            break
                    
                    if target_student:
                        print(f"找到目标学生:")
                        print(f"  - 姓名: {target_student.get('real_name')}")
                        print(f"  - StudentInfo ID: {target_student.get('id')}")
                        print(f"  - Student ID: {target_student.get('student_id')}")
                        print(f"  - 当前班级ID: {target_student.get('class_id')}")
                        print(f"  - 当前班级名: {target_student.get('class_name')}")
                        print(f"  - 是否已选科: {target_student.get('is_track_selected')}")
                        print(f"  - 原班级ID: {target_student.get('previous_class_id')}")

                        # 现在尝试移除（使用StudentInfo的主键ID）
                        class_id = target_student.get('class_id')
                        student_id = target_student.get('id')
                        
                        print(f"\n尝试从班级 {class_id} 移除学生 {student_id}")
                        
                        remove_response = requests.delete(
                            f'http://localhost:8000/api/classes/{class_id}/students/{student_id}/',
                            headers=headers,
                            timeout=10
                        )
                        
                        print(f"移除操作状态码: {remove_response.status_code}")
                        
                        if remove_response.status_code == 200:
                            result = remove_response.json()
                            print(f"✅ 移除成功: {result.get('message')}")
                        else:
                            try:
                                error_result = remove_response.json()
                                print(f"❌ 移除失败: {error_result}")
                            except:
                                print(f"❌ 移除失败: {remove_response.text}")
                    else:
                        print(f"❌ 未找到学生ID=2")
            
        else:
            print(f"❌ 登录失败: {login_response.text}")
    
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    simple_test()
