#!/usr/bin/env python3
"""
使用正确的班级ID测试移除学生
"""

import requests
import json

def test_correct_removal():
    """使用正确的班级ID测试移除学生"""
    print("🔍 使用正确的班级ID测试移除学生")
    print("="*50)
    
    # 1. 登录获取token
    login_data = {
        'username': 'admin',
        'password': 'admin123'
    }
    
    try:
        login_response = requests.post('http://localhost:8000/api/auth/login', data=login_data, timeout=10)
        
        if login_response.status_code == 200:
            login_result = login_response.json()
            token = login_result.get('access_token')
            headers = {'Authorization': f'Bearer {token}', 'Content-Type': 'application/json'}
            
            print(f"✅ 登录成功")
            
            # 2. 获取学生34的当前信息
            students_response = requests.get(
                'http://localhost:8000/api/students/?limit=50',
                headers=headers,
                timeout=10
            )
            
            if students_response.status_code == 200:
                students_data = students_response.json()
                students = students_data['data']['items']
                
                # 查找学生34
                target_student = None
                for student in students:
                    if student.get('id') == 34:
                        target_student = student
                        break
                
                if target_student:
                    print(f"\n找到学生34的当前信息:")
                    print(f"  - 姓名: {target_student.get('real_name')}")
                    print(f"  - StudentInfo ID: {target_student.get('id')}")
                    print(f"  - 当前班级ID: {target_student.get('class_id')}")
                    print(f"  - 当前班级名: {target_student.get('class_name')}")
                    print(f"  - 是否已选科: {target_student.get('is_track_selected')}")
                    print(f"  - 原班级ID: {target_student.get('previous_class_id')}")
                    
                    # 3. 使用正确的班级ID进行移除
                    current_class_id = target_student.get('class_id')
                    student_id = target_student.get('id')
                    
                    if current_class_id:
                        print(f"\n尝试从班级 {current_class_id} 移除学生 {student_id}")
                        
                        remove_response = requests.delete(
                            f'http://localhost:8000/api/classes/{current_class_id}/students/{student_id}/',
                            headers=headers,
                            timeout=10
                        )
                        
                        print(f"移除操作状态码: {remove_response.status_code}")
                        print(f"移除操作响应: {remove_response.text}")
                        
                        if remove_response.status_code == 200:
                            response_data = remove_response.json()
                            if response_data.get('code') == 200:
                                print("✅ 移除成功!")
                                
                                # 4. 验证移除结果 - 重新获取学生信息
                                print(f"\n验证移除结果...")
                                verify_response = requests.get(
                                    'http://localhost:8000/api/students/?limit=50',
                                    headers=headers,
                                    timeout=10
                                )
                                
                                if verify_response.status_code == 200:
                                    verify_data = verify_response.json()
                                    verify_students = verify_data['data']['items']
                                    
                                    verify_student = None
                                    for student in verify_students:
                                        if student.get('id') == 34:
                                            verify_student = student
                                            break
                                    
                                    if verify_student:
                                        print(f"移除后的学生信息:")
                                        print(f"  - 当前班级ID: {verify_student.get('class_id')}")
                                        print(f"  - 当前班级名: {verify_student.get('class_name')}")
                                        print(f"  - 状态: {verify_student.get('status_desc')}")
                            else:
                                print(f"❌ 移除失败: {response_data.get('message')}")
                        else:
                            print(f"❌ 移除请求失败: {remove_response.text}")
                    else:
                        print("❌ 学生当前没有分配班级")
                else:
                    print("❌ 没有找到学生34")
            else:
                print(f"❌ 获取学生列表失败: {students_response.text}")
        else:
            print(f"❌ 登录失败: {login_response.text}")
    
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_correct_removal()
