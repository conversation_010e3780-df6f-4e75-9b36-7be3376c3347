from typing import List, Optional, Any
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from backend.api.deps import get_current_active_user, check_permissions
from backend.db.database import get_db
from backend.db.models.user import SysUser, UserRole, TeacherAssignment
from backend.db.models.teacher import TeacherInfo
from backend.db.models.class_student import ClassInfo, StudentInfo
from backend.db.models.subject import SubjectInfo
from backend.api.v1.endpoints.roles import ROLE_NAMES
from sqlalchemy import text
from backend.api.v1.schemas.user import UserCreate, UserUpdate, UserResponse, UserListResponse
from backend.core.security import hash_password, get_current_user
from backend.utils.response import success_response, error_response, paginated_response
from backend.schemas.common import PaginationParams
from backend.core.logger import logger

router = APIRouter()

@router.get("/", response_model=Any)
def get_users(
    db: Session = Depends(get_db),
    current_user: SysUser = Depends(get_current_active_user),
    skip: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=100),
    username: Optional[str] = None,
    real_name: Optional[str] = None,
    status: Optional[int] = None,
    role_type: Optional[str] = None,
):
    """获取用户列表"""
    try:
        # 构建基础查询
        query = db.query(SysUser)
        
        # 应用过滤条件
        if username:
            query = query.filter(SysUser.username.like(f"%{username}%"))
        if real_name:
            query = query.filter(SysUser.real_name.like(f"%{real_name}%"))
        if status is not None:
            query = query.filter(SysUser.status == status)
        if role_type:
            query = query.filter(SysUser.role_type == role_type)
        
        # 获取总数
        total = query.count()
        
        # 分页查询用户数据
        users = query.offset(skip).limit(limit).all()
        
        # 设置角色信息（基于新的 role_type 字段）
        from backend.api.v1.endpoints.roles import ROLE_NAMES
        for user in users:
            if hasattr(user, 'role_type') and user.role_type:
                try:
                    role_enum = UserRole(user.role_type)
                    user.role_name = ROLE_NAMES.get(role_enum, "未知角色")
                    user.role_code = user.role_type
                except ValueError:
                    user.role_name = "未知角色"
                    user.role_code = user.role_type
            else:
                user.role_name = "未分配"
                user.role_code = None
        
        # 转换为字典格式
        user_list = []
        for user in users:
            # 获取角色中文名称
            role_name = "未知角色"
            try:
                if hasattr(user.role_type, 'value'):
                    # 如果是枚举类型
                    role_name = ROLE_NAMES.get(user.role_type, user.role_type.value)
                else:
                    # 如果是字符串类型，需要找到对应的枚举
                    for role_enum, name in ROLE_NAMES.items():
                        if role_enum.value == user.role_type:
                            role_name = name
                            break
                    else:
                        role_name = user.role_type
            except:
                role_name = str(user.role_type)

            user_dict = {
                "id": user.id,
                "username": user.username,
                "real_name": user.real_name,
                "email": user.email,
                "mobile": user.mobile,
                "gender": user.gender,
                "avatar": user.avatar,
                "status": user.status,
                "role_name": role_name,
                "role_code": getattr(user, "role_code", None),
                "role_type": user.role_type,
                "managed_grade": getattr(user, 'managed_grade', None),
                "managed_grade_name": {1: '高一', 2: '高二', 3: '高三'}.get(getattr(user, 'managed_grade', None), None),
                "created_at": user.created_at,
                "updated_at": user.updated_at
            }

            # 如果是教师，获取班级分配信息
            if user.role_type == UserRole.TEACHER or user.role_type == 'teacher':
                # 获取教师信息
                teacher_info = db.query(TeacherInfo).filter(TeacherInfo.user_id == user.id).first()
                if teacher_info:
                    # 获取班级分配
                    assignments = db.query(TeacherAssignment).filter(
                        TeacherAssignment.teacher_id == teacher_info.id,
                        TeacherAssignment.status == 1
                    ).all()

                    if assignments:
                        # 按班级分组处理教师职责
                        class_roles = {}  # {class_id: {'name': 'xxx', 'is_head': False, 'subjects': []}}

                        for assignment in assignments:
                            class_info = db.query(ClassInfo).filter(ClassInfo.id == assignment.class_id).first()
                            subject_info = db.query(SubjectInfo).filter(SubjectInfo.id == assignment.subject_id).first()

                            if class_info:
                                if assignment.class_id not in class_roles:
                                    class_roles[assignment.class_id] = {
                                        'name': class_info.name,
                                        'is_head': False,
                                        'subjects': []
                                    }

                                # 检查是否为班主任
                                if assignment.is_main_teacher:
                                    class_roles[assignment.class_id]['is_head'] = True

                                # 添加任课科目（避免重复）
                                if subject_info and subject_info.name not in class_roles[assignment.class_id]['subjects']:
                                    class_roles[assignment.class_id]['subjects'].append(subject_info.name)

                        # 生成显示文本
                        teaching_info = []
                        for class_data in class_roles.values():
                            class_desc = []

                            # 班主任职责
                            if class_data['is_head']:
                                class_desc.append(f"{class_data['name']}班主任")

                            # 任课科目
                            for subject in class_data['subjects']:
                                class_desc.append(f"{class_data['name']}{subject}老师")

                            teaching_info.extend(class_desc)

                        user_dict["class_name"] = ", ".join(teaching_info) if teaching_info else "未分配任课"
                    else:
                        user_dict["class_name"] = "未分配任课"
                else:
                    user_dict["class_name"] = "未分配任课"

            # 如果是学生，获取班级信息
            elif user.role_type == UserRole.STUDENT or user.role_type == 'student':
                # 获取学生班级信息（从StudentInfo表）
                student_info = db.query(StudentInfo).filter(StudentInfo.user_id == user.id).first()
                if student_info and student_info.class_id:
                    class_info = db.query(ClassInfo).filter(ClassInfo.id == student_info.class_id).first()
                    if class_info:
                        user_dict["class_name"] = f"{class_info.name}学生"
                    else:
                        user_dict["class_name"] = "未分配班级"
                else:
                    user_dict["class_name"] = "未分配班级"

            else:
                user_dict["class_name"] = None

            user_list.append(user_dict)
        
        return {
            "code": 200,
            "message": "获取用户列表成功",
            "data": {
                "total": total,
                "items": user_list
            }
        }
        
    except Exception as e:
        logger.error(f"获取用户列表失败: {e}")
        return {
            "code": 500,
            "message": f"获取用户列表失败: {str(e)}",
            "data": None
        }

@router.post("/", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
def create_user(
    db: Session = Depends(get_db),
    current_user: SysUser = Depends(get_current_active_user),
    user_in: UserCreate = None,
):
    """创建用户"""
    # 检查权限
    check_permissions(["system:user:add"])(current_user)
    
    try:
        # 检查用户名是否已存在
        user = db.query(SysUser).filter(SysUser.username == user_in.username).first()
        if user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户名已存在",
            )
        
        # 创建用户
        hashed_password = hash_password(user_in.password)
        db_user = SysUser(
            username=user_in.username,
            password=hashed_password,
            real_name=user_in.real_name,
            gender=user_in.gender,
            email=user_in.email,
            mobile=user_in.mobile,
            avatar=user_in.avatar,
            role_type=user_in.role_type,
            status=user_in.status,
            created_by=current_user.id,
            updated_by=current_user.id,
        )
        db.add(db_user)
        db.commit()
        db.refresh(db_user)
        
        # 角色已经在创建用户时设置，无需额外处理
        
        return {
            "code": 200,
            "message": "创建用户成功",
            "data": db_user
        }
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建用户失败: {str(e)}"
        )

@router.get("/{user_id}", response_model=UserResponse)
def get_user(
    user_id: int,
    db: Session = Depends(get_db),
    current_user: SysUser = Depends(get_current_active_user),
):
    """获取用户详情"""
    # 检查权限
    check_permissions(["system:user:query"])(current_user)
    
    try:
        # 获取用户详情
        user = db.query(SysUser).filter(SysUser.id == user_id).first()
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
            
        # 设置角色信息
        from backend.api.v1.endpoints.roles import ROLE_NAMES
        if hasattr(user, 'role_type') and user.role_type:
            try:
                role_enum = UserRole(user.role_type)
                role_name = ROLE_NAMES.get(role_enum, "未知角色")
            except ValueError:
                role_name = "未知角色"
        else:
            role_name = "未分配"

        user_data = {
            "id": user.id,
            "username": user.username,
            "real_name": user.real_name,
            "gender": user.gender,
            "email": user.email,
            "mobile": user.mobile,
            "avatar": user.avatar,
            "status": user.status,
            "role_type": user.role_type,
            "role_name": role_name,
            "last_login_time": user.last_login_time,
            "last_login_ip": user.last_login_ip,
            "created_at": user.created_at,
            "updated_at": user.updated_at
        }

        return {
            "code": 200,
            "message": "获取用户详情成功",
            "data": user_data
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取用户详情失败: {str(e)}"
        )

@router.put("/{user_id}")
def update_user(
    user_id: int,
    user_in: UserUpdate,
    db: Session = Depends(get_db),
    current_user: SysUser = Depends(get_current_active_user),
):
    """更新用户"""
    # 检查权限
    check_permissions(["system:user:edit"])(current_user)
    
    try:
        # 获取用户
        db_user = db.query(SysUser).filter(SysUser.id == user_id).first()
        if not db_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
            
        # 更新用户信息
        update_data = user_in.model_dump(exclude_unset=True)
        if "password" in update_data:
            update_data["password"] = hash_password(update_data["password"])
        
        for field, value in update_data.items():
            setattr(db_user, field, value)
            
        db_user.updated_by = current_user.id
        db.commit()
        db.refresh(db_user)
        
        # 角色已经在更新用户时设置，无需额外处理
                
        return {
            "code": 200,
            "message": "更新用户成功",
            "data": db_user
        }
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新用户失败: {str(e)}"
        )

@router.delete("/{user_id}")
def delete_user(
    user_id: int,
    db: Session = Depends(get_db),
    current_user: SysUser = Depends(get_current_active_user),
):
    """删除用户"""
    # 检查权限
    check_permissions(["system:user:delete"])(current_user)
    
    # 获取用户
    db_user = db.query(SysUser).filter(SysUser.id == user_id).first()
    if not db_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )

    # 检查是否为管理员账号
    if (db_user.role_type == UserRole.ADMIN or db_user.role_type == 'admin' or
        db_user.username == 'admin'):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="管理员账号无法删除"
        )

    try:
        # 删除相关的关联数据
        logger.info(f"开始删除用户 {db_user.username} (ID: {user_id})")

        # 1. 删除用户角色关联（如果存在旧的权限系统数据）
        try:
            # 尝试删除可能存在的旧权限系统关联数据
            db.execute(text("DELETE FROM sys_user_role WHERE user_id = :user_id"), {"user_id": user_id})
            logger.info(f"删除用户角色关联数据")
        except Exception as e:
            logger.info(f"跳过用户角色关联删除: {e}")

        # 2. 删除教师相关数据（不管角色类型，都检查是否有教师数据）
        # 删除教师任课分配
        assignments_deleted = db.execute(
            text("DELETE ta FROM teacher_assignment ta INNER JOIN teacher_info ti ON ta.teacher_id = ti.id WHERE ti.user_id = :user_id"),
            {"user_id": user_id}
        ).rowcount
        if assignments_deleted > 0:
            logger.info(f"删除 {assignments_deleted} 条教师任课分配记录")

        # 删除教师信息
        teacher_deleted = db.execute(
            text("DELETE FROM teacher_info WHERE user_id = :user_id"),
            {"user_id": user_id}
        ).rowcount
        if teacher_deleted > 0:
            logger.info(f"删除 {teacher_deleted} 条教师信息记录")

        # 3. 删除学生相关数据（不管角色类型，都检查是否有学生数据）
        # 删除学生班级关联
        student_classes_deleted = db.execute(
            text("DELETE FROM student_class WHERE student_id = :user_id"),
            {"user_id": user_id}
        ).rowcount
        if student_classes_deleted > 0:
            logger.info(f"删除 {student_classes_deleted} 条学生班级关联记录")

        # 删除学生信息
        student_info_deleted = db.execute(
            text("DELETE FROM student_info WHERE user_id = :user_id"),
            {"user_id": user_id}
        ).rowcount
        if student_info_deleted > 0:
            logger.info(f"删除 {student_info_deleted} 条学生信息记录")

        # 4. 最后删除用户
        db.delete(db_user)
        db.commit()

        logger.info(f"用户 {db_user.username} 删除成功")
        return {
            "code": 200,
            "message": "删除用户成功",
            "data": None
        }

    except Exception as e:
        db.rollback()
        logger.error(f"删除用户失败: {str(e)}")

        # 根据错误类型返回更友好的错误信息
        error_msg = str(e)
        if "foreign key constraint fails" in error_msg.lower():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="删除失败：该用户存在关联数据，请先处理相关数据后再删除"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"删除用户失败: {str(e)}"
            )