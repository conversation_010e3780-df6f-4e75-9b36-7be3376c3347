from sqlalchemy import Column, BigInteger, String, Integer, <PERSON><PERSON><PERSON>, Text, ForeignKey, DateTime, UniqueConstraint
from sqlalchemy.orm import relationship
from backend.db.database import Base
from backend.db.base_model import BaseModel
import bcrypt
import enum

class UserRole(enum.Enum):
    """用户角色枚举"""
    STUDENT = "student"  # 学生
    TEACHER = "teacher"  # 教师（包括班主任和科任教师）
    GRADE_DIRECTOR = "grade_director"  # 年级主任
    ACADEMIC_DIRECTOR = "academic_director"  # 教务主任
    PRINCIPAL = "principal"  # 校长
    ADMIN = "admin"  # 系统管理员

class SysUser(Base, BaseModel):
    """
    用户表 - 简化版本，直接包含角色信息
    """
    __tablename__ = "sys_user"
    __table_args__ = {'extend_existing': True}

    username = Column(String(50), nullable=False, unique=True, comment="用户名")
    password = Column(String(128), nullable=False, comment="密码(加密存储)")
    real_name = Column(String(50), nullable=False, index=True, comment="真实姓名")
    role_type = Column(String(20), nullable=False, index=True, comment="用户角色类型")
    managed_grade = Column(Integer, nullable=True, comment="管理的年级(1=高一,2=高二,3=高三,NULL=不管理年级)")
    gender = Column(Integer, nullable=False, default=0, comment="性别(0:未知, 1:男, 2:女)")
    email = Column(String(100), nullable=True, comment="电子邮箱")
    mobile = Column(String(20), nullable=True, index=True, comment="手机号码")
    avatar = Column(String(255), nullable=True, comment="头像URL")
    status = Column(Integer, nullable=False, default=1, index=True, comment="状态(0:禁用, 1:启用)")
    last_login_time = Column(DateTime, nullable=True, comment="最后登录时间")
    last_login_ip = Column(String(50), nullable=True, comment="最后登录IP")

    # 关联关系
    teacher_info = relationship("TeacherInfo", back_populates="user", uselist=False)
    student_info = relationship("StudentInfo", back_populates="user", uselist=False)

    def verify_password(self, plain_password: str) -> bool:
        """验证密码是否正确"""
        return bcrypt.checkpw(plain_password.encode(), self.password.encode())

    def is_student(self) -> bool:
        """是否为学生"""
        return self.role_type == UserRole.STUDENT

    def is_teacher(self) -> bool:
        """是否为教师"""
        return self.role_type == UserRole.TEACHER or self.role_type == 'teacher'

    def is_manager(self) -> bool:
        """是否为管理层"""
        return self.role_type in [UserRole.GRADE_DIRECTOR, UserRole.ACADEMIC_DIRECTOR, UserRole.PRINCIPAL]

    def __repr__(self):
        role_display = self.role_type.value if hasattr(self.role_type, 'value') else self.role_type
        return f"<User {self.username}({role_display})>"


class TeacherAssignment(Base, BaseModel):
    """
    教师任教关系表
    """
    __tablename__ = "teacher_assignment"
    __table_args__ = {'extend_existing': True}

    teacher_id = Column(BigInteger, ForeignKey("teacher_info.id"), nullable=False, index=True, comment="教师ID")
    class_id = Column(BigInteger, ForeignKey("class_info.id"), nullable=False, index=True, comment="班级ID")
    subject_id = Column(BigInteger, ForeignKey("subject_info.id"), nullable=False, index=True, comment="科目ID")
    semester_id = Column(BigInteger, ForeignKey("sys_semester.id"), nullable=False, index=True, comment="学期ID")
    is_main_teacher = Column(Boolean, nullable=False, default=True, comment="是否主讲教师(0:否, 1:是)")
    weekly_periods = Column(Integer, nullable=True, comment="周课时数")
    status = Column(Integer, nullable=False, default=1, index=True, comment="状态(0:无效, 1:有效)")

    # 关联关系
    teacher = relationship("TeacherInfo", back_populates="teacher_assignments")
    class_info = relationship("ClassInfo", back_populates="teacher_assignments")
    subject = relationship("SubjectInfo", back_populates="teacher_assignments")

    # 定义唯一约束
    __table_args__ = (
        UniqueConstraint('teacher_id', 'class_id', 'subject_id', 'semester_id',
                        name='uk_teacher_class_subject_semester'),
        {'extend_existing': True}
    )

    def __repr__(self):
        return f"<TeacherAssignment {self.teacher_id}-{self.class_id}-{self.subject_id}>"


