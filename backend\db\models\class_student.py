from sqlalchemy import Column, Big<PERSON><PERSON>ger, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Foreign<PERSON>ey, JSON, UniqueConstraint
from sqlalchemy.orm import relationship
from backend.db.database import Base
from backend.db.base_model import BaseModel

class ClassInfo(Base, BaseModel):
    """
    班级信息表
    """
    __tablename__ = "class_info"
    
    name = Column(String(50), nullable=False, comment="班级名称")
    grade = Column(Integer, nullable=False, index=True, comment="年级(1:高一, 2:高二, 3:高三)")
    class_type = Column(Integer, nullable=False, index=True, comment="班级类型(0:未分科, 1:物理类, 2:历史类)")
    head_teacher_id = Column(BigInteger, ForeignKey("teacher_info.id"), nullable=True, index=True, comment="班主任ID")
    semester_id = Column(BigInteger, ForeignKey("sys_semester.id"), nullable=False, index=True, comment="学期ID")
    student_count = Column(Integer, nullable=False, default=0, comment="学生人数")
    classroom = Column(String(50), nullable=True, comment="教室")
    status = Column(Integer, nullable=False, default=1, index=True, comment="状态(0:禁用, 1:启用)")
    
    # 定义唯一约束
    __table_args__ = (
        UniqueConstraint('name', 'semester_id', name='uk_name_semester_id'),
        {'extend_existing': True}
    )
    
    # 关联关系
    head_teacher = relationship("TeacherInfo", back_populates="head_teacher_classes")
    semester = relationship("SysSemester", back_populates="classes")
    students = relationship("StudentInfo",
                           primaryjoin="and_(StudentInfo.class_id==ClassInfo.id, StudentInfo.is_deleted==False)",
                           back_populates="class_info",
                           foreign_keys="[StudentInfo.class_id]")
    teacher_assignments = relationship("TeacherAssignment", back_populates="class_info")
    
    def __repr__(self):
        return f"<Class {self.name}>"


class StudentInfo(Base, BaseModel):
    """
    学生信息表
    """
    __tablename__ = "student_info"
    __table_args__ = {'extend_existing': True}
    
    user_id = Column(BigInteger, ForeignKey("sys_user.id"), unique=True, nullable=False, comment="用户ID")
    student_no = Column(String(20), unique=True, nullable=False, comment="学号")
    class_id = Column(BigInteger, ForeignKey("class_info.id"), nullable=True, index=True, comment="班级ID")
    enrollment_year = Column(Integer, nullable=False, index=True, comment="入学年份")
    is_track_selected = Column(Boolean, nullable=False, default=False, index=True, comment="是否已完成选科(0:否, 1:是)")
    track_type = Column(Integer, nullable=True, index=True, comment="选科类型(1:物理类, 2:历史类)")
    # 选科数据结构 - 优化后只保留必要字段
    track_selection = Column(JSON, nullable=True, comment="完整选科信息")
    second_choice_subjects = Column(JSON, nullable=True, comment="再选科目(2门)")
    foreign_language = Column(String(50), nullable=True, comment="外语选择")
    combination_name = Column(String(100), nullable=True, comment="选科组合名称")
    track_selection_time = Column(Date, nullable=True, comment="选科时间")

    # 兼容性字段（逐步废弃）
    selected_subjects = Column(JSON, nullable=True, comment="选择的科目(兼容性字段)")

    # 注意：first_choice_subject 字段已移除，使用 track_type 动态计算
    previous_class_id = Column(BigInteger, ForeignKey("class_info.id"), nullable=True, index=True, comment="选科前班级ID")
    status = Column(Integer, nullable=False, default=1, index=True, comment="状态(0:休学, 1:在校, 2:毕业, 3:转学)")
    
    # 关联关系
    user = relationship("SysUser", back_populates="student_info")
    class_info = relationship("ClassInfo", foreign_keys=[class_id], back_populates="students")
    previous_class = relationship("ClassInfo", foreign_keys=[previous_class_id])
    
    def __repr__(self):
        return f"<Student {self.student_no}>"


