#!/usr/bin/env python3
"""
调试学生数据结构
"""

import requests
import json

def debug_student_data():
    """调试学生数据结构"""
    print("🔍 调试学生数据结构")
    print("="*50)
    
    # 1. 登录获取token
    login_data = {
        'username': 'admin',
        'password': 'admin123'
    }
    
    try:
        login_response = requests.post('http://localhost:8000/api/auth/login', data=login_data, timeout=10)
        
        if login_response.status_code == 200:
            login_result = login_response.json()
            token = login_result.get('access_token')
            headers = {'Authorization': f'Bearer {token}', 'Content-Type': 'application/json'}
            
            print(f"✅ 登录成功")
            
            # 2. 获取学生列表
            students_response = requests.get(
                'http://localhost:8000/api/students/?limit=10',
                headers=headers,
                timeout=10
            )
            
            if students_response.status_code == 200:
                students_data = students_response.json()
                print(f"✅ 学生列表获取成功")
                print(f"响应数据结构: {json.dumps(students_data, indent=2, ensure_ascii=False)}")
                
                if 'data' in students_data and 'items' in students_data['data']:
                    students = students_data['data']['items']
                    print(f"\n学生数量: {len(students)}")
                    
                    for i, student in enumerate(students[:3]):
                        print(f"\n学生 {i+1}:")
                        for key, value in student.items():
                            print(f"  {key}: {value}")
                else:
                    print(f"❌ 学生数据格式不正确")
            else:
                print(f"❌ 学生列表获取失败: {students_response.text}")
        else:
            print(f"❌ 登录失败: {login_response.text}")
    
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_student_data()
