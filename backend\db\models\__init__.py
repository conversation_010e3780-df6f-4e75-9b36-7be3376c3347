"""
数据模型模块
"""

# 数据模型集合
__all__ = [
    "SysUser",
    "UserRole",
    "TeacherAssignment",
    "StudentClass",
    "ClassInfo",
    "StudentInfo",
    "TeacherInfo",
    "TeacherSubjectGroup",
    "SubjectInfo",
    "SysSemester",
    "ExamInfo",
    "ExamSubject",
    "ExamScoreLine",
    "StudentScoreUnified"
]

# 导入所有模型
from .user import SysUser, UserRole, TeacherAssignment
from .semester import SysSemester
from .teacher import TeacherInfo, TeacherSubjectGroup
from .subject import SubjectInfo
from .class_student import ClassInfo, StudentInfo
from .exam import ExamInfo, ExamSubject, ExamScoreLine
from .unified_score import StudentScoreUnified

# 待实现的其他模型
# from backend.db.models.teacher import TeacherInfo, TeacherSubjectGroup
# from backend.db.models.class_info import ClassInfo
# from backend.db.models.student import StudentInfo
# from backend.db.models.subject import SubjectInfo
# from backend.db.models.teacher_assignment import TeacherAssignment
# from backend.db.models.score import StudentScore, StudentTotalScore 