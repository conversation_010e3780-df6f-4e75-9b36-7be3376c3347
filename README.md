# 成绩管理系统

## 项目概述

成绩管理系统是一个专为学校设计的成绩录入、分析和管理平台，支持新高考选科模式下的成绩管理需求。

### 系统最新更新

**UI界面现代化改进**
- 重新设计主页界面，采用现代化卡片式布局
- 优化用户体验，使用渐变背景和毛玻璃效果
- 简化权限管理流程，分离角色分配和班级管理

**权限管理优化**
- 权限管理页面专注于用户角色分配
- 班级分配和科目教授关系移至班级管理模块
- 合并班主任和科任教师为统一的"教师"角色
- 具体职责通过班级管理中的分配来区分
- 提高管理效率，避免功能重复

**系统错误修复**
- 修复了 TeacherAssignment 模型字段不匹配导致的500错误
- 恢复了完整的教师分配功能，包括学期、主讲教师、课时等字段
- 修复了 SQLAlchemy 模型关系错误，解决了数据库映射问题
- 修复了权限检查系统，适配简化的角色权限模型
- 修复了用户更新API的响应验证错误
- 修复了数据库 role_type 字段长度限制问题
- 修复了前端API请求URL重定向问题
- 解决了用户保存时跳转到登录页面的问题
- 确保所有API正常工作，系统稳定运行

**数据架构重大优化（2025-07-27）**
- **统一班级分配数据源**：移除了 `StudentClass` 表，统一使用 `StudentInfo` 表管理学生班级分配
- **解决数据一致性问题**：消除了双表管理导致的数据不一致和查询复杂性
- **修复死锁问题**：实现了数据库死锁重试机制和优化的事务处理
- **优化并发性能**：批量操作现在按班级ID排序更新，避免死锁产生
- **简化业务逻辑**：学生班级管理流程更加清晰和可维护

## 角色权限说明

### 用户角色类型
- **学生 (student)**: 在校学习的学生，只能查看自己的成绩和相关信息
- **教师 (teacher)**: 负责教学工作的教师，包括班主任和科任教师，具体职责通过班级管理分配
- **年级主任 (grade_director)**: 负责管理整个年级的教学管理人员
- **教务主任 (academic_director)**: 负责全校教务管理的管理人员
- **校长 (principal)**: 学校最高管理者，拥有所有权限

### 教师职责区分
- **班主任**: 在班级管理中设置某个教师为班级的班主任
- **科任教师**: 在班级管理中设置某个教师教授某个班级的某个科目

这种设计更符合实际的学校管理流程，避免了权限角色的重复和混乱。

**重要更新：统一成绩表重构**

为解决之前分散的成绩表（原始分数表、赋分数据表、总分数据表）导致的数据一致性问题和查询复杂度问题，系统正在进行统一成绩表重构。

主要改进：
- 使用单一统一表管理所有类型的成绩数据
- 优化数据结构和查询性能
- 简化API设计，提高系统可维护性
- 减少数据冗余和不一致风险

查看[实施进度](./implementation_progress.md)了解重构详情。

## 系统功能

### 1. 基础信息管理
- 学生信息管理
- 班级信息管理
- 科目信息管理
- 考试信息管理

### 2. 成绩管理
- 成绩录入与批量导入
- 成绩修改与审核
- 成绩查询与导出
- 总分计算与排名

### 3. 数据分析与报表
- 班级成绩报表
- 学科成绩分析
- 考试整体分析
- 学生个体分析
- **校级报告**（新增）
  - 整体概况统计
  - 班级对比分析
  - 上线分析统计

### 4. 选科管理
- 选科组合设置
- 学生选科管理
- 选科统计分析
   - 选科建议生成

### 5. 权限管理
- 用户角色管理
- 权限分配与控制
- 操作日志审计

## API文档

### 统一成绩API（新）

#### 查询类API

##### 1. 获取班级学生成绩
- **路径**：`/api/v1/unified-scores/class-scores/`
- **方法**：GET
- **参数**：
  - `exam_id`: 考试ID
  - `class_id`: 班级ID
  - `subject_id`: 科目ID
- **返回**：指定班级学生在指定考试中的指定科目成绩列表

##### 2. 获取成绩分布
- **路径**：`/api/v1/unified-scores/distribution/`
- **方法**：GET
- **参数**：
  - `exam_id`: 考试ID
  - `subject_id`: 科目ID
  - `grade_id`: 年级ID（可选）
  - `class_id`: 班级ID（可选）
- **返回**：成绩分布数据

##### 3. 获取班级对比数据
- **路径**：`/api/v1/unified-scores/comparison/classes/`
- **方法**：GET
- **参数**：
  - `exam_id`: 考试ID
  - `subject_id`: 科目ID
  - `grade_id`: 年级ID（可选）
- **返回**：班级间成绩对比数据

##### 4. 获取考试总体统计
- **路径**：`/api/v1/unified-scores/analysis/exam-overview/`
- **方法**：GET
- **参数**：
  - `exam_id`: 考试ID
  - `track_type`: 方向类型（可选，1=物理方向，2=历史方向）
- **返回**：考试的总体统计数据

##### 5. 获取班级对比数据（校级报告专用）
- **路径**：`/api/v1/unified-scores/class-comparison/`
- **方法**：GET
- **参数**：
  - `exam_id`: 考试ID
  - `subject_id`: 科目ID
  - `grade_id`: 年级ID（可选）
  - `track_type`: 方向类型（可选，1=物理方向，2=历史方向）
- **返回**：班级间成绩对比数据，包含平均分、及格率、优秀率等
- **特性**：自动筛选，只返回有对应方向学生的班级数据

#### 录入类API

##### 1. 录入单个学生成绩
- **路径**：`/api/v1/unified-scores/`
- **方法**：POST
- **参数**：
  ```json
  {
    "student_id": 123,
    "exam_id": 456,
    "subject_id": 789,
    "raw_score": 90.5,
    "status": 1,
    "remark": "优秀"
  }
  ```
- **返回**：创建或更新的成绩记录

##### 2. 批量录入成绩
- **路径**：`/api/v1/unified-scores/bulk/`
- **方法**：POST
- **参数**：
  ```json
  {
    "exam_id": 456,
    "subject_id": 789,
    "scores": [
      {
        "student_id": 123,
        "raw_score": 90.5,
        "status": 1
      },
      {
        "student_id": 124,
        "raw_score": 85.0,
        "status": 1
      }
    ]
  }
  ```
- **返回**：批量创建或更新的成绩记录

## 环境要求

- **后端**：Python 3.9+, FastAPI, SQLAlchemy
- **前端**：Vue 3, TypeScript, Element Plus
- **数据库**：MySQL 8.0
- **缓存**：Redis (可选)

## 安装部署

详细的安装和部署指南请参考[部署文档](./docs/deployment.md)。

## 贡献指南

如需贡献代码，请参考[贡献指南](./docs/contributing.md)。

## 许可证

本项目采用[MIT许可证](./LICENSE)。 