"""
学生状态管理工具类
"""

from typing import Optional, Dict, Any
from sqlalchemy.orm import Session
from backend.db.models.class_student import StudentInfo, ClassInfo
from backend.db.models.user import SysUser

class StudentStatusManager:
    """学生状态管理器"""
    
    # 学生状态常量
    STATUS_NO_RECORD = "未建立学籍"
    STATUS_NO_CLASS = "未分配行政班"
    STATUS_IN_ADMIN_CLASS = "在行政班级中"
    STATUS_SELECTED_WAITING = "已选科，待重新分班"
    STATUS_REASSIGNED = "已重新分班"
    STATUS_GRADUATED = "已毕业"
    STATUS_SUSPENDED = "休学"
    STATUS_TRANSFERRED = "转学"
    
    # 状态图标
    STATUS_ICONS = {
        STATUS_NO_RECORD: "🆕",
        STATUS_NO_CLASS: "📝", 
        STATUS_IN_ADMIN_CLASS: "📚",
        STATUS_SELECTED_WAITING: "✅",
        STATUS_REASSIGNED: "🔄",
        STATUS_GRADUATED: "🎓",
        STATUS_SUSPENDED: "⏸️",
        STATUS_TRANSFERRED: "📤"
    }
    
    @staticmethod
    def _serialize_class_info(class_info):
        """序列化班级信息"""
        if not class_info:
            return None
        return {
            "id": class_info.id,
            "name": class_info.name,
            "grade": class_info.grade,
            "student_count": class_info.student_count
        }

    @staticmethod
    def _serialize_student_info(student_info):
        """序列化学生信息"""
        if not student_info:
            return None
        return {
            "id": student_info.id,
            "user_id": student_info.user_id,
            "student_no": student_info.student_no,
            "enrollment_year": student_info.enrollment_year,
            "class_id": student_info.class_id,
            "is_track_selected": student_info.is_track_selected,
            "track_type": student_info.track_type,
            "track_selection": student_info.track_selection,
            "track_selection_time": student_info.track_selection_time.isoformat() if student_info.track_selection_time else None,
            "status": student_info.status,
            "previous_class_id": student_info.previous_class_id
        }

    @staticmethod
    def get_student_status(db: Session, user_id: int) -> Dict[str, Any]:
        """
        获取学生完整状态信息

        Args:
            db: 数据库会话
            user_id: 用户ID

        Returns:
            包含学生状态信息的字典
        """
        # 获取用户信息
        user = db.query(SysUser).filter(SysUser.id == user_id).first()
        if not user or user.role_type != 'student':
            return {
                "status": "非学生用户",
                "status_code": "NOT_STUDENT",
                "icon": "❌",
                "description": "该用户不是学生角色"
            }
        
        # 获取学生学籍信息
        student_info = db.query(StudentInfo).filter(StudentInfo.user_id == user_id).first()
        if not student_info:
            return {
                "status": StudentStatusManager.STATUS_NO_RECORD,
                "status_code": "NO_RECORD",
                "icon": StudentStatusManager.STATUS_ICONS[StudentStatusManager.STATUS_NO_RECORD],
                "description": "用户已注册但未建立学籍档案",
                "actions": ["建立学籍"],
                "student_info": None,
                "admin_class": None,
                "current_class": None
            }
        
        # 检查是否分配了行政班级
        if not student_info.class_id:
            return {
                "status": StudentStatusManager.STATUS_NO_CLASS,
                "status_code": "NO_CLASS",
                "icon": StudentStatusManager.STATUS_ICONS[StudentStatusManager.STATUS_NO_CLASS],
                "description": "已建立学籍但未分配行政班级",
                "student_info": StudentStatusManager._serialize_student_info(student_info),
                "admin_class": None,
                "current_class": None,
                "actions": ["分配行政班"]
            }
        
        # 获取当前班级信息（直接从StudentInfo表）
        current_class = None
        if student_info.class_id:
            current_class = db.query(ClassInfo).filter(ClassInfo.id == student_info.class_id).first()

        # 获取原行政班级信息
        admin_class = None
        if student_info.previous_class_id:
            admin_class = db.query(ClassInfo).filter(ClassInfo.id == student_info.previous_class_id).first()
        elif not student_info.is_track_selected and student_info.class_id:
            # 如果还没选科，当前班级就是行政班级
            admin_class = current_class
        
        # 根据学生状态判断
        if student_info.status == 2:  # 已毕业
            return {
                "status": StudentStatusManager.STATUS_GRADUATED,
                "status_code": "GRADUATED",
                "icon": StudentStatusManager.STATUS_ICONS[StudentStatusManager.STATUS_GRADUATED],
                "description": f"已从{admin_class.name if admin_class else '未知班级'}毕业",
                "student_info": StudentStatusManager._serialize_student_info(student_info),
                "admin_class": StudentStatusManager._serialize_class_info(admin_class),
                "current_class": StudentStatusManager._serialize_class_info(current_class),
                "actions": []
            }
        elif student_info.status == 0:  # 休学
            return {
                "status": StudentStatusManager.STATUS_SUSPENDED,
                "status_code": "SUSPENDED",
                "icon": StudentStatusManager.STATUS_ICONS[StudentStatusManager.STATUS_SUSPENDED],
                "description": f"从{admin_class.name if admin_class else '未知班级'}休学",
                "student_info": StudentStatusManager._serialize_student_info(student_info),
                "admin_class": StudentStatusManager._serialize_class_info(admin_class),
                "current_class": StudentStatusManager._serialize_class_info(current_class),
                "actions": ["恢复学籍"]
            }
        elif student_info.status == 3:  # 转学
            return {
                "status": StudentStatusManager.STATUS_TRANSFERRED,
                "status_code": "TRANSFERRED",
                "icon": StudentStatusManager.STATUS_ICONS[StudentStatusManager.STATUS_TRANSFERRED],
                "description": f"已从{admin_class.name if admin_class else '未知班级'}转学",
                "student_info": StudentStatusManager._serialize_student_info(student_info),
                "admin_class": StudentStatusManager._serialize_class_info(admin_class),
                "current_class": StudentStatusManager._serialize_class_info(current_class),
                "actions": []
            }
        
        # 在校学生状态判断
        if not student_info.is_track_selected:
            # 未选科，在行政班级中
            return {
                "status": StudentStatusManager.STATUS_IN_ADMIN_CLASS,
                "status_code": "IN_ADMIN_CLASS",
                "icon": StudentStatusManager.STATUS_ICONS[StudentStatusManager.STATUS_IN_ADMIN_CLASS],
                "description": f"在{admin_class.name if admin_class else '未知班级'}，未选科",
                "student_info": StudentStatusManager._serialize_student_info(student_info),
                "admin_class": StudentStatusManager._serialize_class_info(admin_class),
                "current_class": StudentStatusManager._serialize_class_info(admin_class),  # 当前就在行政班
                "actions": ["选科", "调班"]
            }
        elif not current_assignment:
            # 已选科但未重新分班
            track_name = {1: "物理类", 2: "历史类"}.get(student_info.track_type, "未知类型")
            return {
                "status": StudentStatusManager.STATUS_SELECTED_WAITING,
                "status_code": "SELECTED_WAITING",
                "icon": StudentStatusManager.STATUS_ICONS[StudentStatusManager.STATUS_SELECTED_WAITING],
                "description": f"已选择{track_name}，待重新分班",
                "student_info": StudentStatusManager._serialize_student_info(student_info),
                "admin_class": StudentStatusManager._serialize_class_info(admin_class),
                "current_class": StudentStatusManager._serialize_class_info(admin_class),  # 仍在行政班
                "track_type": track_name,
                "actions": ["重新分班", "修改选科"]
            }
        else:
            # 已重新分班
            track_name = {1: "物理类", 2: "历史类"}.get(student_info.track_type, "未知类型")
            return {
                "status": StudentStatusManager.STATUS_REASSIGNED,
                "status_code": "REASSIGNED",
                "icon": StudentStatusManager.STATUS_ICONS[StudentStatusManager.STATUS_REASSIGNED],
                "description": f"已分配到{track_name}班级：{current_class.name if current_class else '未知班级'}",
                "student_info": StudentStatusManager._serialize_student_info(student_info),
                "admin_class": StudentStatusManager._serialize_class_info(admin_class),
                "current_class": StudentStatusManager._serialize_class_info(current_class),
                "track_type": track_name,
                "actions": ["调班", "修改选科"]
            }
    
    @staticmethod
    def get_students_by_status(db: Session, status_code: str, limit: int = 100) -> list:
        """
        根据状态获取学生列表

        Args:
            db: 数据库会话
            status_code: 状态代码
            limit: 限制数量

        Returns:
            学生列表
        """
        students = []

        # 获取所有学生用户
        all_student_users = db.query(SysUser).filter(SysUser.role_type == 'student').all()

        # 状态代码映射
        status_mapping = {
            "NO_RECORD": StudentStatusManager.STATUS_NO_RECORD,
            "NO_CLASS": StudentStatusManager.STATUS_NO_CLASS,
            "IN_ADMIN_CLASS": StudentStatusManager.STATUS_IN_ADMIN_CLASS,
            "SELECTED_WAITING": StudentStatusManager.STATUS_SELECTED_WAITING,
            "REASSIGNED": StudentStatusManager.STATUS_REASSIGNED,
            "GRADUATED": StudentStatusManager.STATUS_GRADUATED,
            "SUSPENDED": StudentStatusManager.STATUS_SUSPENDED,
            "TRANSFERRED": StudentStatusManager.STATUS_TRANSFERRED
        }

        target_status = status_mapping.get(status_code)
        if not target_status:
            return students

        # 逐个检查学生状态
        count = 0
        for user in all_student_users:
            if count >= limit:
                break

            status_info = StudentStatusManager.get_student_status(db, user.id)
            if status_info.get('status') == target_status:
                students.append(user)
                count += 1

        return students
    
    @staticmethod
    def get_status_statistics(db: Session) -> Dict[str, int]:
        """
        获取各状态学生数量统计

        Args:
            db: 数据库会话

        Returns:
            状态统计字典
        """
        stats = {}

        # 获取所有学生用户
        all_student_users = db.query(SysUser).filter(SysUser.role_type == 'student').all()

        # 逐个检查每个学生的状态
        status_counts = {
            StudentStatusManager.STATUS_NO_RECORD: 0,
            StudentStatusManager.STATUS_NO_CLASS: 0,
            StudentStatusManager.STATUS_IN_ADMIN_CLASS: 0,
            StudentStatusManager.STATUS_SELECTED_WAITING: 0,
            StudentStatusManager.STATUS_REASSIGNED: 0,
            StudentStatusManager.STATUS_GRADUATED: 0,
            StudentStatusManager.STATUS_SUSPENDED: 0,
            StudentStatusManager.STATUS_TRANSFERRED: 0
        }

        for user in all_student_users:
            status_info = StudentStatusManager.get_student_status(db, user.id)
            status = status_info.get('status', '未知')
            if status in status_counts:
                status_counts[status] += 1

        return status_counts
