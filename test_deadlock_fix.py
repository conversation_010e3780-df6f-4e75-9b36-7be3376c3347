#!/usr/bin/env python3
"""
测试死锁修复后的批量分配功能
"""

import requests
import json
import time
import threading

def test_batch_assignment():
    """测试批量分配功能"""
    print("🔍 测试死锁修复后的批量分配功能")
    print("="*50)
    
    # 1. 登录获取token
    login_data = {
        'username': 'admin',
        'password': 'admin123'
    }
    
    try:
        login_response = requests.post('http://localhost:8000/api/auth/login', data=login_data, timeout=10)
        
        if login_response.status_code == 200:
            login_result = login_response.json()
            token = login_result.get('access_token')
            headers = {'Authorization': f'Bearer {token}', 'Content-Type': 'application/json'}
            
            print(f"✅ 登录成功")
            
            # 2. 测试单个批量分配
            print(f"\n📋 测试单个批量分配")
            batch_data = {
                "assignments": [
                    {"student_id": 49, "class_id": 1},
                    {"student_id": 50, "class_id": 2},
                    {"student_id": 51, "class_id": 1}
                ]
            }
            
            batch_response = requests.post(
                'http://localhost:8000/api/students/batch-assign-class/',
                json=batch_data,
                headers=headers,
                timeout=15
            )
            
            print(f"批量分配状态码: {batch_response.status_code}")
            print(f"批量分配响应: {batch_response.text}")
            
            if batch_response.status_code == 200:
                print("✅ 单个批量分配成功")
            else:
                print("❌ 单个批量分配失败")
            
            # 3. 测试并发批量分配（模拟死锁场景）
            print(f"\n🔄 测试并发批量分配")
            
            def concurrent_batch_assign(thread_id, assignments):
                try:
                    batch_data = {"assignments": assignments}
                    response = requests.post(
                        'http://localhost:8000/api/students/batch-assign-class/',
                        json=batch_data,
                        headers=headers,
                        timeout=15
                    )
                    print(f"线程 {thread_id} - 状态码: {response.status_code}")
                    if response.status_code != 200:
                        print(f"线程 {thread_id} - 错误: {response.text}")
                    return response.status_code == 200
                except Exception as e:
                    print(f"线程 {thread_id} - 异常: {e}")
                    return False
            
            # 创建多个并发请求
            threads = []
            results = []
            
            # 线程1：分配学生到班级1和2
            assignments1 = [
                {"student_id": 52, "class_id": 1},
                {"student_id": 53, "class_id": 2}
            ]
            
            # 线程2：分配学生到班级2和1（相反顺序，容易产生死锁）
            assignments2 = [
                {"student_id": 54, "class_id": 2},
                {"student_id": 55, "class_id": 1}
            ]
            
            # 线程3：更多学生分配
            assignments3 = [
                {"student_id": 56, "class_id": 1},
                {"student_id": 57, "class_id": 2}
            ]
            
            def run_thread(thread_id, assignments):
                result = concurrent_batch_assign(thread_id, assignments)
                results.append((thread_id, result))
            
            # 启动并发线程
            thread1 = threading.Thread(target=run_thread, args=(1, assignments1))
            thread2 = threading.Thread(target=run_thread, args=(2, assignments2))
            thread3 = threading.Thread(target=run_thread, args=(3, assignments3))
            
            threads = [thread1, thread2, thread3]
            
            # 同时启动所有线程
            for thread in threads:
                thread.start()
            
            # 等待所有线程完成
            for thread in threads:
                thread.join()
            
            # 检查结果
            success_count = sum(1 for _, success in results if success)
            print(f"\n并发测试结果: {success_count}/{len(results)} 个请求成功")
            
            if success_count == len(results):
                print("✅ 并发批量分配全部成功，死锁问题已解决")
            else:
                print("⚠️ 部分并发请求失败，可能仍有问题")
                
        else:
            print(f"❌ 登录失败: {login_response.text}")
    
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_batch_assignment()
