from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import Any, List, Optional, Dict
from sqlalchemy import func, text

from backend.core.security import get_current_user
from backend.db.session import get_db
from backend.schemas.class_info import (
    ClassCreate,
    ClassUpdate,
    ClassResponse,
    ClassListResponse,
    ClassStudentCountResponse
)
from backend.db.models.class_student import ClassInfo, StudentInfo as Student
from backend.db.models.teacher import TeacherIn<PERSON> as Teacher
from backend.db.models.user import TeacherAssignment
from backend.db.models.subject import SubjectInfo
from backend.db.models.user import SysUser
from backend.utils.response import success_response, error_response, paginated_response
from backend.utils.orm import class_info_to_dict

router = APIRouter(redirect_slashes=False)

@router.get("/", response_model=Any)
def get_classes(
    *,
    db: Session = Depends(get_db),
    current_user: Any = Depends(get_current_user),
    skip: int = 0,
    limit: int = 100,
    semester_id: Optional[int] = None,
    grade: Optional[int] = None,
    class_type: Optional[int] = None,
    head_teacher_id: Optional[int] = None,
    status: Optional[int] = None
) -> Any:
    """
    获取班级列表
    """
    try:
        # 使用简单SQL查询，避免ORM复杂关系可能导致的序列化问题
        sql_query = text("SELECT c.id, c.name, c.grade, c.class_type, c.head_teacher_id, c.semester_id, " \
                "c.student_count, c.classroom, c.status, c.created_at, c.updated_at, c.created_by, c.updated_by, " \
                "s.name as semester_name, COALESCE(u.real_name, '未知') as head_teacher_name " \
                "FROM class_info c " \
                "LEFT JOIN sys_semester s ON c.semester_id = s.id " \
                "LEFT JOIN teacher_info t ON c.head_teacher_id = t.id " \
                "LEFT JOIN sys_user u ON t.user_id = u.id " \
                "WHERE 1=1 ")
        
        params = {}
        
        # 添加过滤条件
        conditions = []
        if semester_id:
            conditions.append("c.semester_id = :semester_id")
            params["semester_id"] = semester_id
        if grade:
            conditions.append("c.grade = :grade")
            params["grade"] = grade
        if class_type is not None:
            conditions.append("c.class_type = :class_type")
            params["class_type"] = class_type
        if head_teacher_id:
            conditions.append("c.head_teacher_id = :head_teacher_id")
            params["head_teacher_id"] = head_teacher_id
        if status is not None:
            conditions.append("c.status = :status")
            params["status"] = status
        
        # 添加条件到查询
        if conditions:
            sql_query = text(str(sql_query) + " AND " + " AND ".join(conditions))
        
        # 添加分页
        sql_query = text(str(sql_query) + " ORDER BY c.id LIMIT :limit OFFSET :offset")
        params["limit"] = limit
        params["offset"] = skip
        
        # 执行查询
        result = db.execute(sql_query, params).fetchall()
        
        # 获取总数
        count_sql = text("SELECT COUNT(*) FROM class_info c WHERE 1=1")
        if conditions:
            count_sql = text(str(count_sql) + " AND " + " AND ".join(conditions))
            
        total = db.execute(count_sql, params).scalar()
        
        # 构建响应
        items = []
        for row in result:
            class_dict = {
                "id": row.id,
                "name": row.name,
                "grade": row.grade,
                "class_type": row.class_type,
                "head_teacher_id": row.head_teacher_id,
                "head_teacher_name": row.head_teacher_name,
                "semester_id": row.semester_id,
                "semester_name": row.semester_name,
                "student_count": row.student_count,
                "classroom": row.classroom,
                "status": row.status,
                "created_at": row.created_at.isoformat() if row.created_at else None,
                "updated_at": row.updated_at.isoformat() if row.updated_at else None,
                "created_by": row.created_by,
                "updated_by": row.updated_by
            }
            items.append(class_dict)
        
        return {
            "code": 200,
            "message": "操作成功",
            "data": {
                "total": total,
                "items": items,
                "page": skip // limit + 1,
                "size": limit
            }
        }
    except Exception as e:
        # 记录错误并返回错误响应
        print(f"获取班级列表失败: {str(e)}")
        return {
            "code": 500,
            "message": f"获取班级列表失败: {str(e)}",
            "data": None
        }

@router.post("/", response_model=Dict[str, Any])
def create_class(
    *,
    db: Session = Depends(get_db),
    current_user: Any = Depends(get_current_user),
    class_in: ClassCreate
) -> Any:
    """
    创建新班级
    """
    # 检查班级名称是否已存在于当前学期
    existing_class = db.query(ClassInfo).filter(
        ClassInfo.name == class_in.name,
        ClassInfo.semester_id == class_in.semester_id
    ).first()
    
    if existing_class:
        return error_response(
            status_code=status.HTTP_400_BAD_REQUEST,
            message="当前学期已存在同名班级"
        )
    
    # 如果有班主任ID，检查班主任是否存在
    if class_in.head_teacher_id:
        head_teacher = db.query(Teacher).filter(
            Teacher.id == class_in.head_teacher_id
        ).first()
        
        if not head_teacher:
            return error_response(
                status_code=status.HTTP_404_NOT_FOUND,
                message="班主任不存在"
            )
        
        # 检查该教师是否已经是其他班级的班主任
        existing_head_class = db.query(ClassInfo).filter(
            ClassInfo.head_teacher_id == class_in.head_teacher_id,
            ClassInfo.semester_id == class_in.semester_id,
            ClassInfo.status == 1
        ).first()
        
        if existing_head_class:
            return error_response(
                status_code=status.HTTP_400_BAD_REQUEST,
                message=f"该教师已经是班级 {existing_head_class.name} 的班主任"
            )
    
    # 创建新班级
    class_info = ClassInfo(
        **class_in.dict(),
        created_by=current_user.id,
        updated_by=current_user.id
    )
    db.add(class_info)
    db.commit()
    db.refresh(class_info)
    
    # 如果指定了班主任，更新教师的班主任状态
    if class_info.head_teacher_id:
        head_teacher = db.query(Teacher).filter(
            Teacher.id == class_info.head_teacher_id
        ).first()
        
        if head_teacher and not head_teacher.is_head_teacher:
            head_teacher.is_head_teacher = True
            db.add(head_teacher)
            db.commit()
    
    # 使用通用工具函数将ORM对象转换为字典
    class_dict = class_info_to_dict(class_info)
    
    return success_response(data=class_dict)

@router.get("/{class_id}", response_model=Dict[str, Any])
def get_class(
    *,
    db: Session = Depends(get_db),
    current_user: Any = Depends(get_current_user),
    class_id: int
) -> Any:
    """
    获取班级详情
    """
    class_info = db.query(ClassInfo).filter(ClassInfo.id == class_id).first()
    if not class_info:
        return error_response(
            status_code=status.HTTP_404_NOT_FOUND,
            message="班级不存在"
        )
    
    # 使用通用工具函数将ORM对象转换为字典
    class_dict = class_info_to_dict(class_info)
    
    return success_response(data=class_dict)

@router.put("/{class_id}/", response_model=Dict[str, Any])
def update_class(
    *,
    db: Session = Depends(get_db),
    current_user: Any = Depends(get_current_user),
    class_id: int,
    class_in: ClassUpdate
) -> Any:
    """
    更新班级信息
    """
    class_info = db.query(ClassInfo).filter(ClassInfo.id == class_id).first()
    if not class_info:
        return error_response(
            status_code=status.HTTP_404_NOT_FOUND,
            message="班级不存在"
        )
    
    # 检查班级名称是否已存在（如果更新了名称）
    if class_in.name and class_in.name != class_info.name:
        existing_class = db.query(ClassInfo).filter(
            ClassInfo.name == class_in.name,
            ClassInfo.semester_id == class_info.semester_id,
            ClassInfo.id != class_id
        ).first()
        
        if existing_class:
            return error_response(
                status_code=status.HTTP_400_BAD_REQUEST,
                message="当前学期已存在同名班级"
            )
    
    # 如果更新了班主任
    old_head_teacher_id = class_info.head_teacher_id
    if class_in.head_teacher_id is not None and class_in.head_teacher_id != old_head_teacher_id:
        # 如果有新班主任，检查班主任是否存在
        if class_in.head_teacher_id:
            head_teacher = db.query(Teacher).filter(
                Teacher.id == class_in.head_teacher_id
            ).first()
            
            if not head_teacher:
                return error_response(
                    status_code=status.HTTP_404_NOT_FOUND,
                    message="班主任不存在"
                )
            
            # 检查该教师是否已经是其他班级的班主任
            existing_head_class = db.query(ClassInfo).filter(
                ClassInfo.head_teacher_id == class_in.head_teacher_id,
                ClassInfo.semester_id == class_info.semester_id,
                ClassInfo.id != class_id,
                ClassInfo.status == 1
            ).first()
            
            if existing_head_class:
                return error_response(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    message=f"该教师已经是班级 {existing_head_class.name} 的班主任"
                )
            
            # 更新新班主任状态
            head_teacher.is_head_teacher = True
            db.add(head_teacher)
        
        # 处理旧班主任状态
        if old_head_teacher_id:
            old_head_teacher = db.query(Teacher).filter(
                Teacher.id == old_head_teacher_id
            ).first()
            
            if old_head_teacher:
                # 检查是否还是其他班级的班主任
                other_classes = db.query(ClassInfo).filter(
                    ClassInfo.head_teacher_id == old_head_teacher_id,
                    ClassInfo.id != class_id,
                    ClassInfo.status == 1
                ).count()
                
                if other_classes == 0:
                    old_head_teacher.is_head_teacher = False
                    db.add(old_head_teacher)
    
    # 更新班级信息
    for field, value in class_in.dict(exclude_unset=True).items():
        setattr(class_info, field, value)
    
    # 更新学生数量
    if class_in.student_count is None:
        student_count = db.query(func.count(Student.id)).filter(
            Student.class_id == class_id,
            Student.status == 1  # 在校学生
        ).scalar()
        class_info.student_count = student_count
    
    class_info.updated_by = current_user.id
    db.add(class_info)
    db.commit()
    db.refresh(class_info)
    
    # 使用通用工具函数将ORM对象转换为字典
    class_dict = class_info_to_dict(class_info)
    
    return success_response(data=class_dict)

# 添加一个带尾部斜杠的路由别名，确保两种URL都能工作
@router.put("/{class_id}/", response_model=Dict[str, Any])
def update_class_with_slash(
    *,
    db: Session = Depends(get_db),
    current_user: Any = Depends(get_current_user),
    class_id: int,
    class_in: ClassUpdate
) -> Any:
    """
    更新班级信息 (带斜杠路径)
    """
    return update_class(db=db, current_user=current_user, class_id=class_id, class_in=class_in)

@router.delete("/{class_id}")
def delete_class(
    *,
    db: Session = Depends(get_db),
    current_user: Any = Depends(get_current_user),
    class_id: int
) -> Any:
    """
    删除班级
    """
    class_info = db.query(ClassInfo).filter(ClassInfo.id == class_id).first()
    if not class_info:
        return error_response(
            status_code=status.HTTP_404_NOT_FOUND,
            message="班级不存在"
        )
    
    # 检查班级是否有学生
    students = db.query(Student).filter(
        Student.class_id == class_id,
        Student.status == 1  # 在校学生
    ).first()
    
    if students:
        return error_response(
            status_code=status.HTTP_400_BAD_REQUEST,
            message="班级中存在学生，无法删除"
        )
    
    # 检查班级是否有教师任课分配
    assignments = db.query(TeacherAssignment).filter(
        TeacherAssignment.class_id == class_id
    ).first()
    
    if assignments:
        return error_response(
            status_code=status.HTTP_400_BAD_REQUEST,
            message="班级存在教师任课分配，无法删除"
        )
    
    # 处理班主任状态
    if class_info.head_teacher_id:
        head_teacher = db.query(Teacher).filter(
            Teacher.id == class_info.head_teacher_id
        ).first()
        
        if head_teacher:
            # 检查是否还是其他班级的班主任
            other_classes = db.query(ClassInfo).filter(
                ClassInfo.head_teacher_id == class_info.head_teacher_id,
                ClassInfo.id != class_id,
                ClassInfo.status == 1
            ).count()
            
            if other_classes == 0:
                head_teacher.is_head_teacher = False
                db.add(head_teacher)
                db.commit()
    
    db.delete(class_info)
    db.commit()
    return success_response(message="班级删除成功")

@router.get("/{class_id}/students/", response_model=dict)
def get_class_students(
    *,
    db: Session = Depends(get_db),
    current_user: Any = Depends(get_current_user),
    class_id: int,
    skip: int = 0,
    limit: int = 100
) -> Any:
    """
    获取班级学生列表
    """
    class_info = db.query(ClassInfo).filter(ClassInfo.id == class_id).first()
    if not class_info:
        return error_response(
            status_code=status.HTTP_404_NOT_FOUND,
            message="班级不存在"
        )
    
    # 使用SQL直接查询而不是返回ORM对象，避免序列化问题
    sql_query = text("""
        SELECT s.id, s.user_id, s.student_no, s.class_id, s.enrollment_year, 
               s.is_track_selected, s.track_type, s.track_selection_time, 
               s.previous_class_id, s.status, s.created_at, s.updated_at,
               s.created_by, s.updated_by, u.real_name, u.gender
        FROM student_info s
        LEFT JOIN sys_user u ON s.user_id = u.id
        WHERE s.class_id = :class_id AND s.status = 1
        ORDER BY s.id
        LIMIT :limit OFFSET :skip
    """)
    
    # 执行查询
    students_result = db.execute(sql_query, {"class_id": class_id, "limit": limit, "skip": skip}).fetchall()
    
    # 获取总数
    total = db.query(Student).filter(
        Student.class_id == class_id,
        Student.status == 1
    ).count()
    
    # 转换结果为字典
    students = []
    for row in students_result:
        student_dict = {
            "id": row.id,
            "user_id": row.user_id,
            "student_no": row.student_no,
            "name": row.real_name,
            "gender": row.gender,
            "class_id": row.class_id,
            "enrollment_year": row.enrollment_year,
            "is_track_selected": row.is_track_selected,
            "track_type": row.track_type,
            "status": row.status,
            "created_at": row.created_at.isoformat() if row.created_at else None,
            "updated_at": row.updated_at.isoformat() if row.updated_at else None
        }
        students.append(student_dict)
    
    return success_response(
        data={
            "class_name": class_info.name,
            "total": total,
            "students": students,
            "page": skip // limit + 1 if limit > 0 else 1,
            "size": limit
        }
    )

@router.get("/{class_id}/teachers/", response_model=dict)
def get_class_teachers(
    *,
    db: Session = Depends(get_db),
    current_user: Any = Depends(get_current_user),
    class_id: int,
    semester_id: Optional[int] = None
) -> Any:
    """
    获取班级教师列表
    """
    class_info = db.query(ClassInfo).filter(ClassInfo.id == class_id).first()
    if not class_info:
        return error_response(
            code=status.HTTP_404_NOT_FOUND,
            message="班级不存在"
        )
    
    # 查询班级的所有任课教师
    query = db.query(TeacherAssignment).filter(
        TeacherAssignment.class_id == class_id
    )
    
    if semester_id:
        query = query.filter(TeacherAssignment.semester_id == semester_id)
    else:
        # 如果未指定学期，使用班级的当前学期
        query = query.filter(TeacherAssignment.semester_id == class_info.semester_id)
    
    assignments = query.all()
    
    # 获取教师和科目信息
    teacher_assignments = []
    for assignment in assignments:
        teacher = db.query(Teacher).filter(Teacher.id == assignment.teacher_id).first()
        subject = db.query(SubjectInfo).filter(SubjectInfo.id == assignment.subject_id).first()
        
        # 查询学期名称
        semester_name = ""
        try:
            from backend.db.models.semester import SysSemester
            semester = db.query(SysSemester).filter(SysSemester.id == assignment.semester_id).first()
            if semester:
                semester_name = semester.name
        except Exception as e:
            print(f"获取学期名称失败: {e}")
        
        if teacher and subject:
            teacher_assignments.append({
                "id": assignment.id,
                "teacher_id": teacher.id,
                "teacher_name": teacher.user.real_name if hasattr(teacher, 'user') and teacher.user else "未知",
                "teacher_no": teacher.teacher_no,
                "subject_id": subject.id,
                "subject_name": subject.name,
                "semester_id": assignment.semester_id,
                "semester_name": semester_name,
                "is_main_teacher": assignment.is_main_teacher,
                "weekly_periods": assignment.weekly_periods,
                "status": assignment.status
            })
    
    print(f"找到班级教师分配: {len(teacher_assignments)} 条记录")
    
    return success_response(
        data={
            "class_name": class_info.name,
            "semester_id": semester_id or class_info.semester_id,
            "teachers": teacher_assignments
        }
    )

@router.get("/{class_id}/statistics/", response_model=Dict[str, Any])
def get_class_statistics(
    *,
    db: Session = Depends(get_db),
    current_user: Any = Depends(get_current_user),
    class_id: int
) -> Any:
    """
    获取班级统计信息
    
    注意: 这个API使用Dict[str, Any]作为response_model是临时解决方案。
    更好的方法是使用utils/response_models.py中的标准响应模型:
    
    ```python
    from backend.utils.response_models import create_response_model
    
    # 1. 定义数据模型
    class ClassStatisticsData(BaseModel):
        class_id: int
        class_name: str
        total_students: int
        male_students: int
        female_students: int
        physics_track: int
        history_track: int
        unselected_track: int
        
        class Config:
            from_attributes = True
    
    # 2. 创建带包装的响应模型
    ClassStatisticsResponse = create_response_model(ClassStatisticsData)
    
    # 3. 使用响应模型
    @router.get("/{class_id}/statistics/", response_model=ClassStatisticsResponse)
    def get_class_statistics(...)
        # ...
        return success_response(data=stats)
    ```
    """
    class_info = db.query(ClassInfo).filter(ClassInfo.id == class_id).first()
    if not class_info:
        return error_response(
            status_code=status.HTTP_404_NOT_FOUND,
            message="班级不存在"
        )
    
    # 获取班级学生总数
    total_students = db.query(func.count(Student.id)).filter(
        Student.class_id == class_id,
        Student.status == 1  # 在校学生
    ).scalar()
    
    # 获取男生数量
    male_students = db.query(func.count(Student.id)).join(
        SysUser, Student.user_id == SysUser.id
    ).filter(
        Student.class_id == class_id,
        Student.status == 1,  # 在校学生
        SysUser.gender == 1  # 男性
    ).scalar()
    
    # 获取女生数量
    female_students = db.query(func.count(Student.id)).join(
        SysUser, Student.user_id == SysUser.id
    ).filter(
        Student.class_id == class_id,
        Student.status == 1,  # 在校学生
        SysUser.gender == 2  # 女性
    ).scalar()
    
    # 获取已选科学生数量
    track_selected_count = db.query(func.count(Student.id)).filter(
        Student.class_id == class_id,
        Student.status == 1,  # 在校学生
        Student.is_track_selected == True
    ).scalar()
    
    # 获取物理类学生数量
    physics_track = db.query(func.count(Student.id)).filter(
        Student.class_id == class_id,
        Student.status == 1,  # 在校学生
        Student.is_track_selected == True,
        Student.track_type == 1  # 物理类
    ).scalar()
    
    # 获取历史类学生数量
    history_track = db.query(func.count(Student.id)).filter(
        Student.class_id == class_id,
        Student.status == 1,  # 在校学生
        Student.is_track_selected == True,
        Student.track_type == 2  # 历史类
    ).scalar()
    
    # 计算未选科学生数量
    unselected_track = total_students - track_selected_count
    
    # 获取班级任课教师数量
    teacher_count = db.query(func.count(func.distinct(TeacherAssignment.teacher_id))).filter(
        TeacherAssignment.class_id == class_id
    ).scalar()

    # 获取班级开设科目数量
    subject_count = db.query(func.count(func.distinct(TeacherAssignment.subject_id))).filter(
        TeacherAssignment.class_id == class_id
    ).scalar()
    
    stats = {
        "class_id": class_id,
        "class_name": class_info.name,
        "total_students": total_students,
        "male_students": male_students,
        "female_students": female_students,
        "physics_track": physics_track,
        "history_track": history_track,
        "unselected_track": unselected_track
    }
    
    return success_response(data=stats)

@router.post("/{class_id}/students/", response_model=Dict[str, Any])
def add_student_to_class(
    *,
    db: Session = Depends(get_db),
    current_user: Any = Depends(get_current_user),
    class_id: int,
    student_data: dict
) -> Any:
    """
    将学生分配到班级
    统一的班级分配接口，支持行政班级和教学班级
    """
    import time
    from sqlalchemy.exc import OperationalError
    from backend.db.models.class_student import StudentInfo

    # 死锁重试机制
    max_retries = 3
    retry_delay = 0.1  # 100ms

    for attempt in range(max_retries):
        try:
            # 检查班级是否存在
            class_info = db.query(ClassInfo).filter(ClassInfo.id == class_id).first()
            if not class_info:
                return error_response(
                    code=404,
                    message="班级不存在"
                )

            student_id = student_data.get('student_id')
            if not student_id:
                return error_response(
                    code=400,
                    message="学生ID不能为空"
                )

            # 检查学生学籍记录是否存在（student_id是StudentInfo的主键）
            student_info = db.query(StudentInfo).filter(StudentInfo.id == student_id).first()
            if not student_info:
                return error_response(
                    code=404,
                    message="学生学籍记录不存在"
                )

            # 检查学生用户是否存在
            student = db.query(SysUser).filter(
                SysUser.id == student_info.user_id,
                SysUser.role_type == 'student'
            ).first()
            if not student:
                return error_response(
                    code=404,
                    message="学生用户不存在"
                )

            # 记录原班级信息
            old_class_id = student_info.class_id
            old_class_info = None
            if old_class_id:
                old_class_info = db.query(ClassInfo).filter(ClassInfo.id == old_class_id).first()

            # 如果是选科后的重新分班，记录原班级
            if student_info.is_track_selected and not student_info.previous_class_id:
                student_info.previous_class_id = old_class_id

            # 更新学生的班级
            student_info.class_id = class_id
            student_info.updated_by = current_user.id

            db.add(student_info)

            # 先提交学生信息的更改
            db.flush()

            # 更新班级学生数量 - 使用排序的班级ID顺序来避免死锁
            class_ids_to_update = []
            if old_class_info and old_class_id != class_id:
                class_ids_to_update.append(old_class_id)
            class_ids_to_update.append(class_id)

            # 按ID排序，确保总是以相同顺序获取锁
            class_ids_to_update = sorted(set(class_ids_to_update))

            for cid in class_ids_to_update:
                class_to_update = db.query(ClassInfo).filter(ClassInfo.id == cid).first()
                if class_to_update:
                    # 重新计算该班级的学生数量
                    current_count = db.query(StudentInfo).filter(StudentInfo.class_id == cid).count()
                    class_to_update.student_count = current_count
                    class_to_update.updated_by = current_user.id
                    db.add(class_to_update)

            db.commit()

            # 判断分配类型
            assignment_type = "teaching_class" if student_info.is_track_selected else "administrative_class"

            return success_response(
                message=f"成功将学生 {student.real_name} 分配到班级 {class_info.name}",
                data={
                    "student_id": student_id,
                    "student_name": student.real_name,
                    "old_class_id": old_class_id,
                    "old_class_name": old_class_info.name if old_class_info else None,
                    "new_class_id": class_id,
                    "new_class_name": class_info.name,
                    "assignment_type": assignment_type,
                    "is_track_selected": student_info.is_track_selected
                }
            )

        except OperationalError as e:
            db.rollback()
            if "Deadlock found" in str(e) and attempt < max_retries - 1:
                print(f"检测到死锁，第 {attempt + 1} 次重试...")
                time.sleep(retry_delay * (2 ** attempt))  # 指数退避
                continue
            else:
                print(f"数据库操作失败: {e}")
                return error_response(
                    code=500,
                    message=f"分配学生失败: 数据库操作错误"
                )
        except Exception as e:
            db.rollback()
            print(f"分配学生失败: {e}")
            import traceback
            traceback.print_exc()
            return error_response(
                code=500,
                message=f"分配学生失败: {str(e)}"
            )

    # 如果所有重试都失败了
    return error_response(
        code=500,
        message="分配学生失败: 数据库繁忙，请稍后重试"
    )

@router.get("/{class_id}/students/{student_id}/test")
def test_route(class_id: int, student_id: int):
    """测试路由"""
    print(f"TEST: 测试路由被调用 - class_id={class_id}, student_id={student_id}")
    return {"message": "测试路由正常工作", "class_id": class_id, "student_id": student_id}

@router.delete("/{class_id}/students/{student_id}/", response_model=Dict[str, Any])
def remove_student_from_class(
    *,
    db: Session = Depends(get_db),
    current_user: Any = Depends(get_current_user),
    class_id: int,
    student_id: int
) -> Any:
    """
    从班级中移除学生
    """
    try:
        from backend.db.models.class_student import StudentInfo

        print(f"DEBUG: 尝试从班级 {class_id} 移除学生 {student_id}")

        # 检查班级是否存在
        class_info = db.query(ClassInfo).filter(ClassInfo.id == class_id).first()
        if not class_info:
            print(f"DEBUG: 班级 {class_id} 不存在")
            return error_response(
                code=404,
                message="班级不存在"
            )

        print(f"DEBUG: 找到班级 {class_info.name}")

        # 检查学生学籍记录
        print(f"DEBUG: 查询学生学籍记录 - student_id={student_id}, class_id={class_id}")

        # 先查询学生是否存在
        student_info_exists = db.query(StudentInfo).filter(StudentInfo.id == student_id).first()
        if student_info_exists:
            print(f"DEBUG: 找到学生学籍记录 - 当前班级ID: {student_info_exists.class_id}")
        else:
            print(f"DEBUG: 未找到学生学籍记录 ID={student_id}")

        student_info = db.query(StudentInfo).filter(
            StudentInfo.id == student_id,
            StudentInfo.class_id == class_id
        ).first()

        if not student_info:
            print(f"DEBUG: 学生 {student_id} 不在班级 {class_id} 中")
            return error_response(
                code=404,
                message="学生不在该班级中"
            )

        print(f"DEBUG: 找到学生学籍记录，是否已选科: {student_info.is_track_selected}, 原班级ID: {student_info.previous_class_id}")

        # 获取学生信息（通过student_info的user_id关联）
        student = db.query(SysUser).filter(SysUser.id == student_info.user_id).first()
        student_name = student.real_name if student else "未知学生"

        print(f"DEBUG: 学生姓名: {student_name}")
    except Exception as e:
        print(f"DEBUG: 移除学生时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return error_response(
            code=500,
            message=f"移除学生时发生错误: {str(e)}"
        )

    # 根据学生状态决定移除逻辑
    if student_info.is_track_selected and student_info.previous_class_id:
        # 已选科且有原班级记录，恢复到原行政班级
        original_class = db.query(ClassInfo).filter(ClassInfo.id == student_info.previous_class_id).first()
        if original_class:
            student_info.class_id = student_info.previous_class_id
            student_info.previous_class_id = None  # 清空历史记录

            # 更新原班级人数
            original_class.student_count = (original_class.student_count or 0) + 1
            db.add(original_class)

            message = f"成功将学生 {student_name} 从教学班级 {class_info.name} 恢复到原行政班级 {original_class.name}"
        else:
            return error_response(
                code=400,
                message=f"无法移除学生 {student_name}：原行政班级不存在"
            )
    else:
        # 未选科学生或没有原班级记录，设为未分配状态
        student_info.class_id = None
        message = f"成功将学生 {student_name} 从班级 {class_info.name} 中移除"

    student_info.updated_by = current_user.id
    db.add(student_info)

    # 先提交学生信息的更改
    db.flush()

    # 更新当前班级学生数量（在学生已经移除后重新计算）
    current_count = db.query(StudentInfo).filter(StudentInfo.class_id == class_id).count()
    class_info.student_count = max(0, current_count)  # 确保不为负数
    class_info.updated_by = current_user.id

    db.add(class_info)
    db.commit()

    return success_response(
        message=message,
        data={
            "student_id": student_id,
            "student_name": student_name,
            "class_id": class_id,
            "class_name": class_info.name,
            "is_track_selected": student_info.is_track_selected
        }
    )

