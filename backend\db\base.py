# 导入数据库基础模型
from backend.db.database import Base

# 导入所有数据库模型
# 注意: 这里的导入是为了确保SQLAlchemy能够识别所有模型类
# 避免循环导入，所以将所有模型导入放在这里统一管理

# 用户相关模型
from .models.user import SysUser, UserRole, TeacherAssignment

# 学期、班级、学生相关模型
from .models.semester import SysSemester
from .models.class_student import ClassInfo, StudentInfo

# 教师和学科相关模型
from .models.teacher import TeacherInfo, TeacherSubjectGroup
from .models.subject import SubjectInfo

# 考试和成绩相关模型
from .models.exam import ExamInfo, ExamSubject, ExamScoreLine

# 所有模型
__all__ = [
    "Base",
    "SysUser",
    "UserRole",
    "TeacherAssignment",
    "ClassInfo",
    "StudentInfo",
    "TeacherInfo",
    "TeacherSubjectGroup",
    "SubjectInfo",
    "ExamInfo",
    "ExamSubject",
    "ExamScoreLine",
    "SysSemester"
]
