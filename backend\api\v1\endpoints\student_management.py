"""
学生生命周期管理API
"""

from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from datetime import datetime

from backend.db.database import get_db
from backend.db.models.user import SysUser
from backend.db.models.class_student import StudentInfo, ClassInfo
from backend.api.deps import get_current_user
from backend.utils.response import success_response, error_response, paginated_response
from backend.utils.student_status import StudentStatusManager

router = APIRouter()

@router.get("/status-overview/", response_model=Any)
def get_student_status_overview(
    *,
    db: Session = Depends(get_db),
    current_user: Any = Depends(get_current_user)
) -> Any:
    """
    获取学生状态概览
    """
    try:
        # 获取状态统计
        stats = StudentStatusManager.get_status_statistics(db)
        
        # 获取总学生数
        total_students = db.query(SysUser).filter(SysUser.role_type == 'student').count()
        
        return success_response(
            data={
                "total_students": total_students,
                "status_statistics": stats,
                "status_descriptions": {
                    StudentStatusManager.STATUS_NO_RECORD: {
                        "icon": StudentStatusManager.STATUS_ICONS[StudentStatusManager.STATUS_NO_RECORD],
                        "description": "用户已注册但未建立学籍档案",
                        "actions": ["建立学籍"]
                    },
                    StudentStatusManager.STATUS_NO_CLASS: {
                        "icon": StudentStatusManager.STATUS_ICONS[StudentStatusManager.STATUS_NO_CLASS],
                        "description": "已建立学籍但未分配行政班级",
                        "actions": ["分配行政班"]
                    },
                    StudentStatusManager.STATUS_IN_ADMIN_CLASS: {
                        "icon": StudentStatusManager.STATUS_ICONS[StudentStatusManager.STATUS_IN_ADMIN_CLASS],
                        "description": "在行政班级中，未选科",
                        "actions": ["选科", "调班"]
                    },
                    StudentStatusManager.STATUS_SELECTED_WAITING: {
                        "icon": StudentStatusManager.STATUS_ICONS[StudentStatusManager.STATUS_SELECTED_WAITING],
                        "description": "已选科，待重新分班",
                        "actions": ["重新分班", "修改选科"]
                    },
                    StudentStatusManager.STATUS_REASSIGNED: {
                        "icon": StudentStatusManager.STATUS_ICONS[StudentStatusManager.STATUS_REASSIGNED],
                        "description": "已重新分班到教学班级",
                        "actions": ["调班", "修改选科"]
                    }
                }
            },
            message="获取学生状态概览成功"
        )
        
    except Exception as e:
        return error_response(
            code=500,
            message=f"获取学生状态概览失败: {str(e)}"
        )

@router.get("/by-status/{status_code}/", response_model=Any)
def get_students_by_status(
    *,
    db: Session = Depends(get_db),
    current_user: Any = Depends(get_current_user),
    status_code: str,
    skip: int = 0,
    limit: int = 50
) -> Any:
    """
    根据状态获取学生列表
    """
    try:
        # 获取指定状态的学生
        students = StudentStatusManager.get_students_by_status(db, status_code, limit=limit+skip)
        
        # 分页处理
        paginated_students = students[skip:skip+limit]
        
        # 构建学生详细信息
        student_details = []
        for student in paginated_students:
            status_info = StudentStatusManager.get_student_status(db, student.id)
            
            student_detail = {
                "id": student.id,
                "username": student.username,
                "real_name": student.real_name,
                "gender": student.gender,
                "email": student.email,
                "mobile": student.mobile,
                "status_info": status_info
            }
            
            student_details.append(student_detail)
        
        return paginated_response(
            items=student_details,
            total=len(students),
            page=skip // limit + 1,
            size=limit,
            message=f"获取{status_code}状态学生列表成功"
        )
        
    except Exception as e:
        return error_response(
            code=500,
            message=f"获取学生列表失败: {str(e)}"
        )

@router.post("/create-student-record/", response_model=Any)
def create_student_record(
    *,
    db: Session = Depends(get_db),
    current_user: Any = Depends(get_current_user),
    student_data: dict
) -> Any:
    """
    为用户建立学籍档案
    """
    try:
        user_id = student_data.get('user_id')
        student_no = student_data.get('student_no')
        enrollment_year = student_data.get('enrollment_year')
        
        if not all([user_id, student_no, enrollment_year]):
            return error_response(
                code=400,
                message="用户ID、学号和入学年份不能为空"
            )
        
        # 检查用户是否存在且为学生
        user = db.query(SysUser).filter(
            SysUser.id == user_id,
            SysUser.role_type == 'student'
        ).first()
        
        if not user:
            return error_response(
                code=404,
                message="用户不存在或不是学生角色"
            )
        
        # 检查是否已有学籍记录
        existing_record = db.query(StudentInfo).filter(StudentInfo.user_id == user_id).first()
        if existing_record:
            return error_response(
                code=400,
                message="该学生已有学籍记录"
            )
        
        # 检查学号是否重复
        existing_student_no = db.query(StudentInfo).filter(StudentInfo.student_no == student_no).first()
        if existing_student_no:
            return error_response(
                code=400,
                message="学号已存在"
            )
        
        # 创建学籍记录
        student_info = StudentInfo(
            user_id=user_id,
            student_no=student_no,
            enrollment_year=enrollment_year,
            is_track_selected=False,
            status=1,  # 在校
            created_by=current_user.id,
            updated_by=current_user.id
        )
        
        db.add(student_info)
        db.commit()
        
        return success_response(
            data={
                "user_id": user_id,
                "student_no": student_no,
                "enrollment_year": enrollment_year,
                "student_name": user.real_name
            },
            message=f"成功为 {user.real_name} 建立学籍档案"
        )
        
    except Exception as e:
        db.rollback()
        return error_response(
            code=500,
            message=f"建立学籍档案失败: {str(e)}"
        )

@router.post("/assign-admin-class/", response_model=Any)
def assign_admin_class(
    *,
    db: Session = Depends(get_db),
    current_user: Any = Depends(get_current_user),
    assignment_data: dict
) -> Any:
    """
    分配学生到行政班级
    """
    try:
        user_id = assignment_data.get('user_id')
        class_id = assignment_data.get('class_id')
        
        if not all([user_id, class_id]):
            return error_response(
                code=400,
                message="用户ID和班级ID不能为空"
            )
        
        # 检查学生学籍记录
        student_info = db.query(StudentInfo).filter(StudentInfo.user_id == user_id).first()
        if not student_info:
            return error_response(
                code=404,
                message="学生学籍记录不存在，请先建立学籍"
            )
        
        # 检查班级是否存在
        class_info = db.query(ClassInfo).filter(ClassInfo.id == class_id).first()
        if not class_info:
            return error_response(
                code=404,
                message="班级不存在"
            )
        
        # 更新学籍班级
        old_class_id = student_info.class_id
        student_info.class_id = class_id
        student_info.updated_by = current_user.id
        
        db.add(student_info)
        db.commit()
        
        return success_response(
            data={
                "user_id": user_id,
                "student_name": student_info.user.real_name,
                "old_class_id": old_class_id,
                "new_class_id": class_id,
                "new_class_name": class_info.name
            },
            message=f"成功将 {student_info.user.real_name} 分配到 {class_info.name}"
        )
        
    except Exception as e:
        db.rollback()
        return error_response(
            code=500,
            message=f"分配行政班级失败: {str(e)}"
        )

@router.post("/track-selection/", response_model=Any)
def student_track_selection(
    *,
    db: Session = Depends(get_db),
    current_user: Any = Depends(get_current_user),
    selection_data: dict
) -> Any:
    """
    学生选科
    """
    try:
        user_id = selection_data.get('user_id')
        track_type = selection_data.get('track_type')  # 1: 物理类, 2: 历史类
        track_selection = selection_data.get('track_selection')  # 选科详情
        
        if not all([user_id, track_type]):
            return error_response(
                code=400,
                message="用户ID和选科类型不能为空"
            )
        
        if track_type not in [1, 2]:
            return error_response(
                code=400,
                message="选科类型必须是1(物理类)或2(历史类)"
            )
        
        # 检查学生学籍记录
        student_info = db.query(StudentInfo).filter(StudentInfo.user_id == user_id).first()
        if not student_info:
            return error_response(
                code=404,
                message="学生学籍记录不存在"
            )
        
        if not student_info.class_id:
            return error_response(
                code=400,
                message="学生未分配行政班级，无法选科"
            )
        
        # 更新选科信息 - 支持新的数据结构
        if track_selection:
            first_choice = "物理" if track_type == 1 else "历史"
            second_choices = track_selection.get('second_choices', [])
            foreign_language = track_selection.get('foreign_language', '英语')

            # 兼容旧格式
            if 'subjects' in track_selection:
                # 从旧格式中提取再选科目
                subjects = track_selection.get('subjects', [])
                second_choices = [s for s in subjects if s not in ['语文', '数学', '物理', '历史', foreign_language]]

            # 构建标准化的选科数据
            standardized_selection = {
                "first_choice": first_choice,
                "second_choices": second_choices[:2],  # 最多2门再选科目
                "foreign_language": foreign_language,
                "combination": f"{first_choice}+{'+'.join(second_choices[:2])}+{foreign_language}"
            }

            # 更新学生信息 - 不再设置冗余的 first_choice_subject
            student_info.is_track_selected = True
            student_info.track_type = track_type
            student_info.track_selection = standardized_selection
            student_info.second_choice_subjects = second_choices[:2]
            student_info.foreign_language = foreign_language
            student_info.combination_name = standardized_selection["combination"]
            student_info.track_selection_time = datetime.now().date()
            student_info.updated_by = current_user.id

            # 兼容性字段
            student_info.selected_subjects = second_choices[:2]
        else:
            return error_response(
                code=400,
                message="选科信息不能为空"
            )
        
        db.add(student_info)
        db.commit()
        
        track_type_name = {1: "物理类", 2: "历史类"}[track_type]
        
        return success_response(
            data={
                "user_id": user_id,
                "student_name": student_info.user.real_name,
                "track_type": track_type,
                "track_type_name": track_type_name,
                "track_selection": student_info.track_selection,
                "first_choice_subject": "物理" if track_type == 1 else "历史",  # 动态计算
                "second_choice_subjects": student_info.second_choice_subjects,
                "foreign_language": student_info.foreign_language,
                "combination_name": student_info.combination_name,
                "track_selection_time": student_info.track_selection_time.isoformat()
            },
            message=f"成功为 {student_info.user.real_name} 完成选科：{track_type_name}"
        )
        
    except Exception as e:
        db.rollback()
        return error_response(
            code=500,
            message=f"选科失败: {str(e)}"
        )

@router.post("/reassign-class/", response_model=Any)
def reassign_to_teaching_class(
    *,
    db: Session = Depends(get_db),
    current_user: Any = Depends(get_current_user),
    reassignment_data: dict
) -> Any:
    """
    重新分班到教学班级
    """
    try:
        user_id = reassignment_data.get('user_id')
        teaching_class_id = reassignment_data.get('teaching_class_id')
        
        if not all([user_id, teaching_class_id]):
            return error_response(
                code=400,
                message="用户ID和教学班级ID不能为空"
            )
        
        # 检查学生学籍记录
        student_info = db.query(StudentInfo).filter(StudentInfo.user_id == user_id).first()
        if not student_info:
            return error_response(
                code=404,
                message="学生学籍记录不存在"
            )
        
        if not student_info.is_track_selected:
            return error_response(
                code=400,
                message="学生未完成选科，无法重新分班"
            )
        
        # 检查教学班级是否存在
        teaching_class = db.query(ClassInfo).filter(ClassInfo.id == teaching_class_id).first()
        if not teaching_class:
            return error_response(
                code=404,
                message="教学班级不存在"
            )
        
        # 检查学生当前班级状态
        old_class = None
        if student_info.class_id:
            old_class = db.query(ClassInfo).filter(ClassInfo.id == student_info.class_id).first()

        # 记录原行政班级（如果是第一次分配教学班）
        if not student_info.previous_class_id and student_info.class_id:
            student_info.previous_class_id = student_info.class_id

        # 更新学生的教学班分配
        student_info.class_id = teaching_class_id
        student_info.updated_by = current_user.id
        db.add(student_info)

        if old_class:
            message = f"成功将 {student_info.user.real_name} 从 {old_class.name} 调整到教学班 {teaching_class.name}"
        else:
            message = f"成功将 {student_info.user.real_name} 分配到教学班 {teaching_class.name}"
        
        db.commit()
        
        return success_response(
            data={
                "user_id": user_id,
                "student_name": student_info.user.real_name,
                "teaching_class_id": teaching_class_id,
                "teaching_class_name": teaching_class.name,
                "admin_class_id": student_info.class_id,
                "admin_class_name": db.query(ClassInfo).filter(ClassInfo.id == student_info.class_id).first().name if student_info.class_id else None
            },
            message=message
        )
        
    except Exception as e:
        db.rollback()
        return error_response(
            code=500,
            message=f"重新分班失败: {str(e)}"
        )
